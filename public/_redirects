# Redirects and Rewrites for Tuffside Website

# Redirect old URLs to new structure (if migrating from another site)
# /old-about /about/ 301
# /old-services /services/ 301
# /old-contact /contact/ 301

# Handle trailing slashes consistently
/about /about/ 301
/services /services/ 301
/contact /contact/ 301
/bookings /bookings/ 301
/testimonials /testimonials/ 301

# Redirect common misspellings or alternative URLs
/service /services/ 301
/booking /bookings/ 301
/testimonial /testimonials/ 301

# Handle www redirects (uncomment and modify as needed)
# https://www.tuffside.com/* https://tuffside.com/:splat 301!

# Fallback for SPA routing (Next.js handles this, but good to have as backup)
/* /index.html 200
