"use strict";(()=>{var e={};e.id=432,e.ids=[432],e.modules={846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5860:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var a={};t.r(a),t.d(a,{GET:()=>A,dynamic:()=>u});var n=t(6559),o=t(8088),s=t(7719),i=t(2190);let p=Buffer.from("iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAIAAAD8GO2jAAABgGlDQ1BzUkdCIElFQzYxOTY2LTIuMQAAKJF1kc8rRFEUxz+GacSIYkFZvITVkB8lNspMQkkaoww2M8+bGTVvvN57kyZbZTtFiY1fC/4CtspaKSIla7bEhuk5z6iRzLndcz/3e8853XsueCJpVbeqekDP2GZ4LKjMRecV3zNeWqjARyCmWsbI9PQkZe39ViLFrrvcWuXj/rXaJc1SoaJaeFg1TFt4XHhy1TZc3hJuUlOxJeET4YApFxS+cfV4kZ9cThb502UzEg6Bp0FYSf7i+C9WU6YuLC+nXU9n1Z/7uC/xa5nZGVnbZLZiEWaMIAoTjBJigF6GxA/QRR/dsqNMfs93/hQrkquKN8hhskySFDYBUbNSXZM1IbomI03O7f/fvlqJ/r5idX8QvI+O89oBvk0o5B3n48BxCodQ+QDnmVL+yj4MvomeL2nte1C/DqcXJS2+DWcb0HxvxMzYt1Qp05NIwMsx1EWh8QpqFoo9+znn6A4ia/JVl7CzC50SX7/4BTLZZ87E7Xg7AAAACXBIWXMAAD2EAAA9hAHVrK90AAAByUlEQVRIie2TvariQBiGZ5cllT+DIJJOlBSCJBcgRE1hI4KF9sGrEPESRFCw0cZOQdDGRlt/IDailWIj02ihqCgxGpxsMazILu6ZrHvYLXyq4ZuZ532HEADevPnv+UJ5ThAEURQ5jrPb7QzDqKqqqur5fL7+4HK56LoOIbRYLOl02kSFVCo1HA5vt5tBjcfjoVJDCBuNBr2XMJ1OHyXffhNQKpUSiQQAYLVaDQaD0Wi0XC5Zli0UCgihcDhstVohhA6Hw+VyOZ1Om8223W7r9TpVfVmWMcb7/T6bzTIMc5/3+33SNJPJUImeUavVDMOQZflxGI1GMcYkIB6PvxSgKMp8Pn/sDgDodDrEvl6vf9p6xtNvMJvNWJa9Xq/3SSAQkCSJrNvt9uPWnxCLxXRdDwaD90mz2ST1d7ud2+1+yU7I5/O9Xs/v9wMAeJ7XNI0EFItFeskHf7IkSaFQCCEUiUSSySQA4HA48DyPEHqh+S94vd7j8UjqVyqVv6km5HI5Yj+dThzHmbr7leaQz+cji1artVgsTBf8kPF4bBiGpmmCIJi9S/WCzWYDAOh2u5PJ5FMCqtUqxlhRFLN2E5TLZVEUPzHgzZt/yHdSejfzmQfRzAAAAABJRU5ErkJggg==","base64");function A(){return new i.NextResponse(p,{headers:{"Content-Type":"image/png","Cache-Control":"public, immutable, no-transform, max-age=31536000"}})}let u="force-static",c=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/apple-icon.png/route",pathname:"/apple-icon.png",filename:"apple-icon",bundlePath:"app/apple-icon.png/route"},resolvedPagePath:"next-metadata-route-loader?filePath=%2FUsers%2Fedsin%2FWorkspace%2FAPP%2FTuffside%2Fsrc%2Fapp%2Fapple-icon.png&isDynamicRouteExtension=0!?__next_metadata_route__",nextConfigOutput:"export",userland:a}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:x}=c;function v(){return(0,s.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},9294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[447,580],()=>t(5860));module.exports=a})();