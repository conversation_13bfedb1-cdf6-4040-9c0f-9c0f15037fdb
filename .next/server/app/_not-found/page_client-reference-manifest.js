globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/_not-found/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"894":{"*":{"id":"6346","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"7173","name":"*","chunks":[],"async":false}},"3063":{"*":{"id":"6533","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"8827","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"7924","name":"*","chunks":[],"async":false}},"6614":{"*":{"id":"5656","name":"*","chunks":[],"async":false}},"6874":{"*":{"id":"5814","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"99","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"8243","name":"*","chunks":[],"async":false}},"9024":{"*":{"id":"4899","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"2763","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/client/components/client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/esm/client/components/client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/client/components/client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/esm/client/components/client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/client/components/error-boundary.js":{"id":6614,"name":"*","chunks":[],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":6614,"name":"*","chunks":[],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/client/components/layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/esm/client/components/layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/client/components/metadata/async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/client/components/render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/client/app-dir/link.js":{"id":6874,"name":"*","chunks":["177","static/chunks/app/layout-b7ea9e5aafa6667a.js"],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/esm/client/app-dir/link.js":{"id":6874,"name":"*","chunks":["177","static/chunks/app/layout-b7ea9e5aafa6667a.js"],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.js\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-inter\",\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":2552,"name":"*","chunks":["177","static/chunks/app/layout-b7ea9e5aafa6667a.js"],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.js\",\"import\":\"Mr_Dafoe\",\"arguments\":[{\"variable\":\"--font-mr-dafoe\",\"subsets\":[\"latin\"],\"weight\":\"400\"}],\"variableName\":\"mrDafoe\"}":{"id":3652,"name":"*","chunks":["177","static/chunks/app/layout-b7ea9e5aafa6667a.js"],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/src/app/globals.css":{"id":347,"name":"*","chunks":["177","static/chunks/app/layout-b7ea9e5aafa6667a.js"],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/src/components/Footer.module.css":{"id":4689,"name":"*","chunks":["177","static/chunks/app/layout-b7ea9e5aafa6667a.js"],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/src/components/Header.js":{"id":9024,"name":"*","chunks":["177","static/chunks/app/layout-b7ea9e5aafa6667a.js"],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/src/components/CTAButtons.module.css":{"id":8869,"name":"*","chunks":["63","static/chunks/63-1a000242a02acb1a.js","974","static/chunks/app/page-ca2cb00c64d384cd.js"],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/src/app/contact/page.module.css":{"id":8231,"name":"*","chunks":[],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/src/app/about/page.module.css":{"id":6848,"name":"*","chunks":[],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/client/image-component.js":{"id":3063,"name":"*","chunks":["63","static/chunks/63-1a000242a02acb1a.js","974","static/chunks/app/page-ca2cb00c64d384cd.js"],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/esm/client/image-component.js":{"id":3063,"name":"*","chunks":["63","static/chunks/63-1a000242a02acb1a.js","974","static/chunks/app/page-ca2cb00c64d384cd.js"],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/src/app/page.module.css":{"id":8334,"name":"*","chunks":["63","static/chunks/63-1a000242a02acb1a.js","974","static/chunks/app/page-ca2cb00c64d384cd.js"],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/src/components/BookingCard.module.css":{"id":4295,"name":"*","chunks":[],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/src/app/bookings/page.module.css":{"id":353,"name":"*","chunks":[],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/src/app/services/page.module.css":{"id":9421,"name":"*","chunks":[],"async":false},"/Users/<USER>/Workspace/APP/Tuffside/src/app/testimonials/page.module.css":{"id":4777,"name":"*","chunks":[],"async":false}},"entryCSSFiles":{"/Users/<USER>/Workspace/APP/Tuffside/src/":[],"/Users/<USER>/Workspace/APP/Tuffside/src/app/layout":[{"inlined":false,"path":"static/css/24f907d8e8b232ca.css"}],"/Users/<USER>/Workspace/APP/Tuffside/src/app/page":[{"inlined":false,"path":"static/css/45fe9974673e481a.css"}],"/Users/<USER>/Workspace/APP/Tuffside/src/app/_not-found/page":[]},"rscModuleMapping":{"347":{"*":{"id":"1135","name":"*","chunks":[],"async":false}},"353":{"*":{"id":"8000","name":"*","chunks":[],"async":false}},"894":{"*":{"id":"6444","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"1307","name":"*","chunks":[],"async":false}},"3063":{"*":{"id":"9603","name":"*","chunks":[],"async":false}},"4295":{"*":{"id":"8738","name":"*","chunks":[],"async":false}},"4689":{"*":{"id":"9768","name":"*","chunks":[],"async":false}},"4777":{"*":{"id":"2304","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"2089","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"6042","name":"*","chunks":[],"async":false}},"6614":{"*":{"id":"8170","name":"*","chunks":[],"async":false}},"6848":{"*":{"id":"7702","name":"*","chunks":[],"async":false}},"6874":{"*":{"id":"4536","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"9477","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"9345","name":"*","chunks":[],"async":false}},"8231":{"*":{"id":"3354","name":"*","chunks":[],"async":false}},"8334":{"*":{"id":"8096","name":"*","chunks":[],"async":false}},"8869":{"*":{"id":"7884","name":"*","chunks":[],"async":false}},"9024":{"*":{"id":"9741","name":"*","chunks":[],"async":false}},"9421":{"*":{"id":"6764","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"6577","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}