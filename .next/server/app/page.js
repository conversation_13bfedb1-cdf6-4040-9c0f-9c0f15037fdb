(()=>{var e={};e.id=974,e.ids=[974],e.modules={512:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return m},defaultHead:function(){return u}});let i=r(4985),n=r(740),s=r(687),a=n._(r(3210)),o=i._(r(7755)),l=r(4959),d=r(9513),c=r(4604);function u(e){void 0===e&&(e=!1);let t=[(0,s.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,s.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function p(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===a.default.Fragment?e.concat(a.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(148);let f=["name","httpEquiv","charSet","itemProp"];function h(e,t){let{inAmpMode:r}=t;return e.reduce(p,[]).reverse().concat(u(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,i={};return n=>{let s=!0,a=!1;if(n.key&&"number"!=typeof n.key&&n.key.indexOf("$")>0){a=!0;let t=n.key.slice(n.key.indexOf("$")+1);e.has(t)?s=!1:e.add(t)}switch(n.type){case"title":case"base":t.has(n.type)?s=!1:t.add(n.type);break;case"meta":for(let e=0,t=f.length;e<t;e++){let t=f[e];if(n.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?s=!1:r.add(t);else{let e=n.props[t],r=i[t]||new Set;("name"!==t||!a)&&r.has(e)?s=!1:(r.add(e),i[t]=r)}}}return s}}()).reverse().map((e,t)=>{let i=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,a.default.cloneElement(e,t)}return a.default.cloneElement(e,{key:i})})}let m=function(e){let{children:t}=e,r=(0,a.useContext)(l.AmpStateContext),i=(0,a.useContext)(d.HeadManagerContext);return(0,s.jsx)(o.default,{reduceComponentsToState:h,headManager:i,inAmpMode:(0,c.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1122:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},1322:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:i,blurHeight:n,blurDataURL:s,objectFit:a}=e,o=i?40*i:t,l=n?40*n:r,d=o&&l?"viewBox='0 0 "+o+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===a?"xMidYMid":"cover"===a?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+s+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},1480:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:i,blurHeight:n,blurDataURL:s,objectFit:a}=e,o=i?40*i:t,l=n?40*n:r,d=o&&l?"viewBox='0 0 "+o+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===a?"xMidYMid":"cover"===a?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+s+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},1933:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:i,width:n,quality:s}=e,a=s||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(i)+"&w="+n+"&q="+a+(i.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}}),r.__next_img_default=!0;let i=r},2091:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:i,width:n,quality:s}=e,a=s||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(i)+"&w="+n+"&q="+a+(i.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}}),r.__next_img_default=!0;let i=r},2480:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return o}});let i=r(2639),n=r(9131),s=r(9603),a=i._(r(2091));function o(e){let{props:t}=(0,n.getImgProps)(e,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=s.Image},2756:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return i}});let r=["default","imgix","cloudinary","akamai","custom"],i={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},2981:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var i=r(7413),n=r(6415),s=r(8096),a=r.n(s),o=r(2480),l=r.n(o);function d(){return(0,i.jsxs)("div",{className:a().homePage,children:[(0,i.jsx)("section",{className:a().hero,children:(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:a().heroContent,children:[(0,i.jsx)(l(),{className:a().heroImage,src:"/images/call-card-graphic.webp",alt:"Tuffside Automotive Garage Logo",width:1e3,height:501}),(0,i.jsxs)("div",{className:a().heroText,children:[(0,i.jsxs)("h1",{className:a().heroTitle,children:["Tuffside",(0,i.jsx)("span",{className:a().heroSubtitle,children:"Automotive Garage"})]}),(0,i.jsx)("p",{className:a().heroTagline,children:"Built to Last. Tuned to Perform."}),(0,i.jsx)("p",{className:a().heroDescription,children:"Professional automotive repair and maintenance services in South Trinidad. Diagnostics, heavy diesel repair, engine tuning, and suspension work with honest, reliable service you can trust."}),(0,i.jsx)(n.A,{layout:"horizontal",size:"large"})]})]})})}),(0,i.jsx)("section",{className:a().servicesPreview,children:(0,i.jsxs)("div",{className:"container",children:[(0,i.jsx)("h2",{className:"text-center mb-lg",children:"Our Services"}),(0,i.jsxs)("div",{className:"grid grid-3",children:[(0,i.jsxs)("div",{className:a().serviceCard,children:[(0,i.jsx)("div",{className:a().serviceIcon,children:"\uD83D\uDD27"}),(0,i.jsx)("h3",{children:"Diagnostics"}),(0,i.jsx)("p",{children:"Advanced diagnostic tools to identify and resolve engine issues quickly and accurately."})]}),(0,i.jsxs)("div",{className:a().serviceCard,children:[(0,i.jsx)("div",{className:a().serviceIcon,children:"⚙️"}),(0,i.jsx)("h3",{children:"Diesel Repair"}),(0,i.jsx)("p",{children:"Specialised diesel engine repair and maintenance for trucks, and heavy machinery."})]}),(0,i.jsxs)("div",{className:a().serviceCard,children:[(0,i.jsx)("div",{className:a().serviceIcon,children:"\uD83C\uDFC1"}),(0,i.jsx)("h3",{children:"Engine Tuning"}),(0,i.jsx)("p",{children:"Performance tuning, modification and optimisation to get the best out of your vehicle's engine."})]})]}),(0,i.jsx)("div",{className:"text-center mt-lg",children:(0,i.jsx)("a",{href:"/services",className:"btn btn-primary btn-large",children:"View All Services"})})]})}),(0,i.jsx)("section",{className:a().whyChooseUs,children:(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:a().whyContent,children:[(0,i.jsxs)("div",{className:a().whyText,children:[(0,i.jsx)("h2",{children:"Why Choose Tuffside?"}),(0,i.jsxs)("ul",{className:a().whyList,children:[(0,i.jsx)("li",{children:"✓ Over 10 years of automotive experience"}),(0,i.jsx)("li",{children:"✓ Honest, transparent pricing"}),(0,i.jsx)("li",{children:"✓ Quick turnaround times"}),(0,i.jsx)("li",{children:"✓ Emergency and on-site services"}),(0,i.jsx)("li",{children:"✓ Friendly service"})]}),(0,i.jsx)("p",{children:"We're not just another garage - we're your neighbours who care about keeping you safely on the road. Every job, big or small, gets our full attention and expertise."})]}),(0,i.jsx)("div",{className:a().whyImage,children:(0,i.jsxs)("div",{className:a().trustBadge,children:[(0,i.jsx)("h3",{children:"Trusted by"}),(0,i.jsx)("p",{className:a().trustNumber,children:"500+"}),(0,i.jsx)("p",{children:"Happy Customers"})]})})]})})}),(0,i.jsx)("section",{className:a().emergency,children:(0,i.jsx)("div",{className:"container",children:(0,i.jsxs)("div",{className:a().emergencyContent,children:[(0,i.jsx)("h2",{children:"Need Emergency Help?"}),(0,i.jsx)("p",{children:"Stuck on the road? We offer emergency call-out services for breakdowns, urgent repairs, and vehicle recovery across Trinidad."}),(0,i.jsxs)("div",{className:a().emergencyActions,children:[(0,i.jsx)("a",{href:"tel:+18683357440",className:"btn btn-primary btn-large",children:"\uD83D\uDCDE Emergency Call"}),(0,i.jsx)("a",{href:"https://wa.me/18683357440",target:"_blank",rel:"noopener noreferrer",className:"btn btn-large",children:"\uD83D\uDCAC WhatsApp Now"})]})]})})})]})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3978:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,9603,23))},4146:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6533,23))},4604:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:i=!1}=void 0===e?{}:e;return t||r&&i}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},4650:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var i=r(1658);let n=async e=>[{type:"image/png",sizes:"32x32",url:(0,i.fillMetadataSegment)(".",await e.params,"apple-icon.png")+"?e33ab4628b95696a"}]},4953:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return l}}),r(148);let i=r(1480),n=r(2756),s=["-moz-initial","fill","none","scale-down",void 0];function a(e){return void 0!==e.default}function o(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var r,l;let d,c,u,{src:p,sizes:f,unoptimized:h=!1,priority:m=!1,loading:g,className:v,quality:_,width:b,height:y,fill:x=!1,style:j,overrideSrc:w,onLoad:C,onLoadingComplete:P,placeholder:E="empty",blurDataURL:S,fetchPriority:O,decoding:N="async",layout:R,objectFit:A,objectPosition:D,lazyBoundary:T,lazyRoot:z,...M}=e,{imgConf:I,showAltText:k,blurComplete:B,defaultLoader:q}=t,W=I||n.imageConfigDefault;if("allSizes"in W)d=W;else{let e=[...W.deviceSizes,...W.imageSizes].sort((e,t)=>e-t),t=W.deviceSizes.sort((e,t)=>e-t),i=null==(r=W.qualities)?void 0:r.sort((e,t)=>e-t);d={...W,allSizes:e,deviceSizes:t,qualities:i}}if(void 0===q)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let G=M.loader||q;delete M.loader,delete M.srcSet;let F="__next_img_default"in G;if(F){if("custom"===d.loader)throw Object.defineProperty(Error('Image with src "'+p+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=G;G=t=>{let{config:r,...i}=t;return e(i)}}if(R){"fill"===R&&(x=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[R];e&&(j={...j,...e});let t={responsive:"100vw",fill:"100vw"}[R];t&&!f&&(f=t)}let U="",L=o(b),X=o(y);if((l=p)&&"object"==typeof l&&(a(l)||void 0!==l.src)){let e=a(p)?p.default:p;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(c=e.blurWidth,u=e.blurHeight,S=S||e.blurDataURL,U=e.src,!x)if(L||X){if(L&&!X){let t=L/e.width;X=Math.round(e.height*t)}else if(!L&&X){let t=X/e.height;L=Math.round(e.width*t)}}else L=e.width,X=e.height}let H=!m&&("lazy"===g||void 0===g);(!(p="string"==typeof p?p:U)||p.startsWith("data:")||p.startsWith("blob:"))&&(h=!0,H=!1),d.unoptimized&&(h=!0),F&&!d.dangerouslyAllowSVG&&p.split("?",1)[0].endsWith(".svg")&&(h=!0);let V=o(_),$=Object.assign(x?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:A,objectPosition:D}:{},k?{}:{color:"transparent"},j),J=B||"empty"===E?null:"blur"===E?'url("data:image/svg+xml;charset=utf-8,'+(0,i.getImageBlurSvg)({widthInt:L,heightInt:X,blurWidth:c,blurHeight:u,blurDataURL:S||"",objectFit:$.objectFit})+'")':'url("'+E+'")',Q=s.includes($.objectFit)?"fill"===$.objectFit?"100% 100%":"cover":$.objectFit,Y=J?{backgroundSize:Q,backgroundPosition:$.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:J}:{},K=function(e){let{config:t,src:r,unoptimized:i,width:n,quality:s,sizes:a,loader:o}=e;if(i)return{src:r,srcSet:void 0,sizes:void 0};let{widths:l,kind:d}=function(e,t,r){let{deviceSizes:i,allSizes:n}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let i;i=e.exec(r);)t.push(parseInt(i[2]));if(t.length){let e=.01*Math.min(...t);return{widths:n.filter(t=>t>=i[0]*e),kind:"w"}}return{widths:n,kind:"w"}}return"number"!=typeof t?{widths:i,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>n.find(t=>t>=e)||n[n.length-1]))],kind:"x"}}(t,n,a),c=l.length-1;return{sizes:a||"w"!==d?a:"100vw",srcSet:l.map((e,i)=>o({config:t,src:r,quality:s,width:e})+" "+("w"===d?e:i+1)+d).join(", "),src:o({config:t,src:r,quality:s,width:l[c]})}}({config:d,src:p,unoptimized:h,width:L,quality:V,sizes:f,loader:G});return{props:{...M,loading:H?"lazy":g,fetchPriority:O,width:L,height:X,decoding:N,className:v,style:{...$,...Y},sizes:K.sizes,srcSet:K.srcSet,src:w||K.src},meta:{unoptimized:h,priority:m,placeholder:E,fill:x}}}},4959:(e,t,r)=>{"use strict";e.exports=r(4041).vendored.contexts.AmpContext},4961:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var i=r(5239),n=r(8088),s=r(8170),a=r.n(s),o=r(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,2981)),"/Users/<USER>/Workspace/APP/Tuffside/src/app/page.js"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,8162))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,4650))).default(e)],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,7779)),"/Users/<USER>/Workspace/APP/Tuffside/src/app/layout.js"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,8162))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,4650))).default(e)],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Workspace/APP/Tuffside/src/app/page.js"],u={require:r,loadChunk:()=>Promise.resolve()},p=new i.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6415:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var i=r(7413),n=r(7884),s=r.n(n);function a({layout:e="horizontal",size:t="default",showBooking:r=!0,className:n=""}){let a=`${s().ctaContainer} ${s()[e]} ${s()[t]} ${n}`;return(0,i.jsxs)("div",{className:a,children:[r&&(0,i.jsx)("a",{href:"/bookings",className:"btn btn-primary btn-large","aria-label":"Book an appointment",children:"Book Appointment"}),(0,i.jsx)("a",{href:"tel:+18683357440",className:"btn btn-large","aria-label":"Call Tuffside Automotive Garage",children:"\uD83D\uDCDE Call Now"}),(0,i.jsx)("a",{href:"https://wa.me/18683357440",target:"_blank",rel:"noopener noreferrer",className:"btn btn-large","aria-label":"Message us on WhatsApp",children:"\uD83D\uDCAC WhatsApp"})]})}},6533:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return y}});let i=r(4985),n=r(740),s=r(687),a=n._(r(3210)),o=i._(r(1215)),l=i._(r(512)),d=r(4953),c=r(2756),u=r(7903);r(148);let p=r(9148),f=i._(r(1933)),h=r(3038),m={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0};function g(e,t,r,i,n,s,a){let o=null==e?void 0:e.src;e&&e["data-loaded-src"]!==o&&(e["data-loaded-src"]=o,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&n(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let i=!1,n=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>i,isPropagationStopped:()=>n,persist:()=>{},preventDefault:()=>{i=!0,t.preventDefault()},stopPropagation:()=>{n=!0,t.stopPropagation()}})}(null==i?void 0:i.current)&&i.current(e)}}))}function v(e){return a.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let _=(0,a.forwardRef)((e,t)=>{let{src:r,srcSet:i,sizes:n,height:o,width:l,decoding:d,className:c,style:u,fetchPriority:p,placeholder:f,loading:m,unoptimized:_,fill:b,onLoadRef:y,onLoadingCompleteRef:x,setBlurComplete:j,setShowAltText:w,sizesInput:C,onLoad:P,onError:E,...S}=e,O=(0,a.useCallback)(e=>{e&&(E&&(e.src=e.src),e.complete&&g(e,f,y,x,j,_,C))},[r,f,y,x,j,E,_,C]),N=(0,h.useMergedRef)(t,O);return(0,s.jsx)("img",{...S,...v(p),loading:m,width:l,height:o,decoding:d,"data-nimg":b?"fill":"1",className:c,style:u,sizes:n,srcSet:i,src:r,ref:N,onLoad:e=>{g(e.currentTarget,f,y,x,j,_,C)},onError:e=>{w(!0),"empty"!==f&&j(!0),E&&E(e)}})});function b(e){let{isAppRouter:t,imgAttributes:r}=e,i={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...v(r.fetchPriority)};return t&&o.default.preload?(o.default.preload(r.src,i),null):(0,s.jsx)(l.default,{children:(0,s.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...i},"__nimg-"+r.src+r.srcSet+r.sizes)})}let y=(0,a.forwardRef)((e,t)=>{let r=(0,a.useContext)(p.RouterContext),i=(0,a.useContext)(u.ImageConfigContext),n=(0,a.useMemo)(()=>{var e;let t=m||i||c.imageConfigDefault,r=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),n=t.deviceSizes.sort((e,t)=>e-t),s=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:r,deviceSizes:n,qualities:s}},[i]),{onLoad:o,onLoadingComplete:l}=e,h=(0,a.useRef)(o);(0,a.useEffect)(()=>{h.current=o},[o]);let g=(0,a.useRef)(l);(0,a.useEffect)(()=>{g.current=l},[l]);let[v,y]=(0,a.useState)(!1),[x,j]=(0,a.useState)(!1),{props:w,meta:C}=(0,d.getImgProps)(e,{defaultLoader:f.default,imgConf:n,blurComplete:v,showAltText:x});return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(_,{...w,unoptimized:C.unoptimized,placeholder:C.placeholder,fill:C.fill,onLoadRef:h,onLoadingCompleteRef:g,setBlurComplete:y,setShowAltText:j,sizesInput:e.sizes,ref:t}),C.priority?(0,s.jsx)(b,{isAppRouter:!r,imgAttributes:w}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7755:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let i=r(3210),n=()=>{},s=()=>{};function a(e){var t;let{headManager:r,reduceComponentsToState:a}=e;function o(){if(r&&r.mountedInstances){let t=i.Children.toArray(Array.from(r.mountedInstances).filter(Boolean));r.updateHead(a(t,e))}}return null==r||null==(t=r.mountedInstances)||t.add(e.children),o(),n(()=>{var t;return null==r||null==(t=r.mountedInstances)||t.add(e.children),()=>{var t;null==r||null==(t=r.mountedInstances)||t.delete(e.children)}}),n(()=>(r&&(r._pendingUpdate=o),()=>{r&&(r._pendingUpdate=o)})),s(()=>(r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null),()=>{r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null)})),null}},7884:e=>{e.exports={ctaContainer:"CTAButtons_ctaContainer__wa_Qd",horizontal:"CTAButtons_horizontal__YYOTX",vertical:"CTAButtons_vertical__sSNK2",default:"CTAButtons_default__vc_us",large:"CTAButtons_large__IH740",small:"CTAButtons_small__qJBJj",btn:"CTAButtons_btn__OqBds"}},7894:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return i}});let r=["default","imgix","cloudinary","akamai","custom"],i={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},7903:(e,t,r)=>{"use strict";e.exports=r(4041).vendored.contexts.ImageConfigContext},8096:e=>{e.exports={homePage:"page_homePage__gmrHv",hero:"page_hero__SKW6o",heroContent:"page_heroContent__2lPR8",heroText:"page_heroText__g5S3T",heroTitle:"page_heroTitle__Gfler",heroSubtitle:"page_heroSubtitle__RTAw0",heroTagline:"page_heroTagline__BFtFD",heroDescription:"page_heroDescription__E0XDR",heroImage:"page_heroImage__Q6NCQ",logoPlaceholder:"page_logoPlaceholder__eMw_y",servicesPreview:"page_servicesPreview__WPoQQ",serviceCard:"page_serviceCard__gtUdi",serviceIcon:"page_serviceIcon__JA4KW",whyChooseUs:"page_whyChooseUs__1lrkb",whyContent:"page_whyContent__BC4vn",whyText:"page_whyText__CDK4H",whyList:"page_whyList__0UxxK",trustBadge:"page_trustBadge__PYQqz",trustNumber:"page_trustNumber__3evPG",emergency:"page_emergency__W_5Pa",emergencyContent:"page_emergencyContent__pq59C",emergencyActions:"page_emergencyActions__nw2qA"}},8162:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var i=r(1658);let n=async e=>[{type:"image/png",sizes:"32x32",url:(0,i.fillMetadataSegment)(".",await e.params,"icon.png")+"?e33ab4628b95696a"}]},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9131:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return l}}),r(1122);let i=r(1322),n=r(7894),s=["-moz-initial","fill","none","scale-down",void 0];function a(e){return void 0!==e.default}function o(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var r,l;let d,c,u,{src:p,sizes:f,unoptimized:h=!1,priority:m=!1,loading:g,className:v,quality:_,width:b,height:y,fill:x=!1,style:j,overrideSrc:w,onLoad:C,onLoadingComplete:P,placeholder:E="empty",blurDataURL:S,fetchPriority:O,decoding:N="async",layout:R,objectFit:A,objectPosition:D,lazyBoundary:T,lazyRoot:z,...M}=e,{imgConf:I,showAltText:k,blurComplete:B,defaultLoader:q}=t,W=I||n.imageConfigDefault;if("allSizes"in W)d=W;else{let e=[...W.deviceSizes,...W.imageSizes].sort((e,t)=>e-t),t=W.deviceSizes.sort((e,t)=>e-t),i=null==(r=W.qualities)?void 0:r.sort((e,t)=>e-t);d={...W,allSizes:e,deviceSizes:t,qualities:i}}if(void 0===q)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let G=M.loader||q;delete M.loader,delete M.srcSet;let F="__next_img_default"in G;if(F){if("custom"===d.loader)throw Object.defineProperty(Error('Image with src "'+p+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=G;G=t=>{let{config:r,...i}=t;return e(i)}}if(R){"fill"===R&&(x=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[R];e&&(j={...j,...e});let t={responsive:"100vw",fill:"100vw"}[R];t&&!f&&(f=t)}let U="",L=o(b),X=o(y);if((l=p)&&"object"==typeof l&&(a(l)||void 0!==l.src)){let e=a(p)?p.default:p;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(c=e.blurWidth,u=e.blurHeight,S=S||e.blurDataURL,U=e.src,!x)if(L||X){if(L&&!X){let t=L/e.width;X=Math.round(e.height*t)}else if(!L&&X){let t=X/e.height;L=Math.round(e.width*t)}}else L=e.width,X=e.height}let H=!m&&("lazy"===g||void 0===g);(!(p="string"==typeof p?p:U)||p.startsWith("data:")||p.startsWith("blob:"))&&(h=!0,H=!1),d.unoptimized&&(h=!0),F&&!d.dangerouslyAllowSVG&&p.split("?",1)[0].endsWith(".svg")&&(h=!0);let V=o(_),$=Object.assign(x?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:A,objectPosition:D}:{},k?{}:{color:"transparent"},j),J=B||"empty"===E?null:"blur"===E?'url("data:image/svg+xml;charset=utf-8,'+(0,i.getImageBlurSvg)({widthInt:L,heightInt:X,blurWidth:c,blurHeight:u,blurDataURL:S||"",objectFit:$.objectFit})+'")':'url("'+E+'")',Q=s.includes($.objectFit)?"fill"===$.objectFit?"100% 100%":"cover":$.objectFit,Y=J?{backgroundSize:Q,backgroundPosition:$.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:J}:{},K=function(e){let{config:t,src:r,unoptimized:i,width:n,quality:s,sizes:a,loader:o}=e;if(i)return{src:r,srcSet:void 0,sizes:void 0};let{widths:l,kind:d}=function(e,t,r){let{deviceSizes:i,allSizes:n}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let i;i=e.exec(r);)t.push(parseInt(i[2]));if(t.length){let e=.01*Math.min(...t);return{widths:n.filter(t=>t>=i[0]*e),kind:"w"}}return{widths:n,kind:"w"}}return"number"!=typeof t?{widths:i,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>n.find(t=>t>=e)||n[n.length-1]))],kind:"x"}}(t,n,a),c=l.length-1;return{sizes:a||"w"!==d?a:"100vw",srcSet:l.map((e,i)=>o({config:t,src:r,quality:s,width:e})+" "+("w"===d?e:i+1)+d).join(", "),src:o({config:t,src:r,quality:s,width:l[c]})}}({config:d,src:p,unoptimized:h,width:L,quality:V,sizes:f,loader:G});return{props:{...M,loading:H?"lazy":g,fetchPriority:O,width:L,height:X,decoding:N,className:v,style:{...$,...Y},sizes:K.sizes,srcSet:K.srcSet,src:w||K.src},meta:{unoptimized:h,priority:m,placeholder:E,fill:x}}}},9148:(e,t,r)=>{"use strict";e.exports=r(4041).vendored.contexts.RouterContext},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9513:(e,t,r)=>{"use strict";e.exports=r(4041).vendored.contexts.HeadManagerContext},9551:e=>{"use strict";e.exports=require("url")},9603:(e,t,r)=>{let{createProxy:i}=r(9844);e.exports=i("/Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/client/image-component.js")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[447,55,658,29],()=>r(4961));module.exports=i})();