(()=>{var e={};e.id=407,e.ids=[407],e.modules={440:(e,r,n)=>{"use strict";n.r(r),n.d(r,{default:()=>i});var o=n(1658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,o.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},631:(e,r,n)=>{"use strict";n.r(r),n.d(r,{GlobalError:()=>t.a,__next_app__:()=>p,pages:()=>l,routeModule:()=>u,tree:()=>d});var o=n(5239),i=n(8088),s=n(8170),t=n.n(s),a=n(893),c={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>a[e]);n.d(r,c);let d={children:["",{children:["bookings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,3741)),"/Users/<USER>/Workspace/APP/Tuffside/src/app/bookings/page.js"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(n.bind(n,7779)),"/Users/<USER>/Workspace/APP/Tuffside/src/app/layout.js"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["/Users/<USER>/Workspace/APP/Tuffside/src/app/bookings/page.js"],p={require:n,loadChunk:()=>Promise.resolve()},u=new o.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/bookings/page",pathname:"/bookings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3255:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3741:(e,r,n)=>{"use strict";n.r(r),n.d(r,{default:()=>l,metadata:()=>d});var o=n(7413),i=n(8738),s=n.n(i);function t({title:e,duration:r,description:n,bookingUrl:i,icon:t,urgent:a=!1}){let c=`${s().bookingCard} ${a?s().urgent:""}`;return(0,o.jsxs)("div",{className:c,children:[(0,o.jsxs)("div",{className:s().cardHeader,children:[(0,o.jsx)("div",{className:s().cardIcon,children:t}),(0,o.jsxs)("div",{className:s().cardTitle,children:[(0,o.jsx)("h3",{children:e}),(0,o.jsxs)("span",{className:s().duration,children:["Duration: ",r]})]})]}),(0,o.jsx)("div",{className:s().cardContent,children:(0,o.jsx)("p",{className:s().description,children:n})}),(0,o.jsx)("div",{className:s().cardFooter,children:(0,o.jsx)("a",{href:i,target:"_blank",rel:"noopener noreferrer",className:`btn ${a?"btn-primary":""} btn-large ${s().bookingButton}`,children:"Book Now"})}),a&&(0,o.jsx)("div",{className:s().urgentBadge,children:"Emergency Service"})]})}var a=n(8e3),c=n.n(a);let d={title:"Book a Service - Tuffside Automotive Garage",description:"Book your automotive service online. Choose from emergency services, repairs, or general servicing. Easy online booking with TidyCal."};function l(){return(0,o.jsxs)("div",{className:c().bookingsPage,children:[(0,o.jsx)("section",{className:c().hero,children:(0,o.jsx)("div",{className:"container",children:(0,o.jsxs)("div",{className:c().heroContent,children:[(0,o.jsx)("h1",{children:"Book a Service"}),(0,o.jsx)("p",{className:c().heroDescription,children:"Choose the service that best fits your needs and book online. We'll confirm your appointment and get your vehicle back in top shape."})]})})}),(0,o.jsx)("section",{className:c().bookingCards,children:(0,o.jsx)("div",{className:"container",children:(0,o.jsx)("div",{className:c().cardsGrid,children:[{title:"Emergency or On-site Services",duration:"1 hour",description:"Stuck on the road or need help at your location? We offer call-outs for breakdowns, urgent repairs, and vehicle recovery. Whether it's a dead battery, a flat tyre, or something more involved, we're equipped to respond quickly and get you moving again.",bookingUrl:"https://tidycal.com/1w7lwem/emergency-or-on-site-services",icon:"\uD83D\uDEA8",urgent:!0},{title:"Repairs",duration:"15 minutes",description:"Whether it's something minor or a bit more serious, we'll take a thorough look and get your vehicle back in shape. This includes engine diagnostics, suspension issues, brake repairs, electrical faults, and other mechanical concerns. Bring it in—we'll sort it out.",bookingUrl:"https://tidycal.com/1w7lwem/repairs",icon:"\uD83D\uDD27",urgent:!1},{title:"General Servicing",duration:"2 hours",description:"Keep your vehicle running smoothly with a routine check-up. This service covers a standard maintenance checklist including oil and filter changes, fluid top-ups, brake inspection, tyre rotation, battery health, and more. Ideal for regular upkeep and peace of mind.",bookingUrl:"https://tidycal.com/1w7lwem/general-servicing",icon:"\uD83D\uDEE0️",urgent:!1}].map((e,r)=>(0,o.jsx)(t,{title:e.title,duration:e.duration,description:e.description,bookingUrl:e.bookingUrl,icon:e.icon,urgent:e.urgent},r))})})}),(0,o.jsx)("section",{className:c().bookingInfo,children:(0,o.jsx)("div",{className:"container",children:(0,o.jsxs)("div",{className:c().infoContent,children:[(0,o.jsxs)("div",{className:c().infoText,children:[(0,o.jsx)("h2",{children:"How It Works"}),(0,o.jsxs)("ol",{className:c().stepsList,children:[(0,o.jsxs)("li",{children:[(0,o.jsx)("strong",{children:"Choose Your Service:"})," Select the type of service that best matches your needs from the options above."]}),(0,o.jsxs)("li",{children:[(0,o.jsx)("strong",{children:"Pick Your Time:"})," Choose a convenient date and time from our available slots."]}),(0,o.jsxs)("li",{children:[(0,o.jsx)("strong",{children:"Provide Details:"})," Tell us about your vehicle and any specific concerns you have."]}),(0,o.jsxs)("li",{children:[(0,o.jsx)("strong",{children:"Confirmation:"})," We'll confirm your booking and send you all the details you need."]})]})]}),(0,o.jsxs)("div",{className:c().contactInfo,children:[(0,o.jsx)("h3",{children:"Need Help Booking?"}),(0,o.jsx)("p",{children:"If you're not sure which service you need or prefer to book over the phone, we're here to help."}),(0,o.jsxs)("div",{className:c().contactOptions,children:[(0,o.jsx)("a",{href:"tel:+18683357440",className:"btn btn-primary",children:"\uD83D\uDCDE Call Us"}),(0,o.jsx)("a",{href:"https://wa.me/18683357440",target:"_blank",rel:"noopener noreferrer",className:"btn",children:"\uD83D\uDCAC WhatsApp"}),(0,o.jsx)("a",{href:"mailto:<EMAIL>",className:"btn",children:"✉️ Email Us"})]})]})]})})}),(0,o.jsx)("section",{className:c().emergency,children:(0,o.jsx)("div",{className:"container",children:(0,o.jsxs)("div",{className:c().emergencyContent,children:[(0,o.jsx)("h2",{children:"\uD83D\uDEA8 Emergency Breakdown?"}),(0,o.jsx)("p",{children:"If you're currently broken down or need immediate assistance, don't wait for an appointment - call us now!"}),(0,o.jsx)("a",{href:"tel:+18683357440",className:"btn btn-primary btn-large",children:"\uD83D\uDCDE Emergency Call: ****** 335-7440"})]})})})]})}},3873:e=>{"use strict";e.exports=require("path")},7167:()=>{},8e3:e=>{e.exports={bookingsPage:"page_bookingsPage__ljLgN",hero:"page_hero__c0n1a",heroContent:"page_heroContent__seQKr",heroDescription:"page_heroDescription__iDNso",bookingCards:"page_bookingCards__KkYNQ",cardsGrid:"page_cardsGrid__6AX3m",bookingInfo:"page_bookingInfo__J7Pur",infoContent:"page_infoContent__aEVv3",infoText:"page_infoText__ourpa",stepsList:"page_stepsList__MDlBI",contactInfo:"page_contactInfo__VQAOc",contactOptions:"page_contactOptions__XS_NU",emergency:"page_emergency__B_Jfx",emergencyContent:"page_emergencyContent__c4s67"}},8738:e=>{e.exports={bookingCard:"BookingCard_bookingCard__WuUmf",urgent:"BookingCard_urgent__pCzbB",cardHeader:"BookingCard_cardHeader__ciU57",cardIcon:"BookingCard_cardIcon__6nQbt",cardTitle:"BookingCard_cardTitle__oTzgC",duration:"BookingCard_duration__Cdcue",cardContent:"BookingCard_cardContent__ndqG9",description:"BookingCard_description__tobEJ",cardFooter:"BookingCard_cardFooter__bMU9z",bookingButton:"BookingCard_bookingButton__aurgb",urgentBadge:"BookingCard_urgentBadge__PJOlj",pulse:"BookingCard_pulse__0oHHt"}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var r=require("../../webpack-runtime.js");r.C(e);var n=e=>r(r.s=e),o=r.X(0,[447,55,658,29],()=>n(631));module.exports=o})();