exports.id=29,exports.ids=[29],exports.modules={936:(e,s,n)=>{Promise.resolve().then(n.t.bind(n,6444,23)),Promise.resolve().then(n.t.bind(n,6042,23)),Promise.resolve().then(n.t.bind(n,8170,23)),Promise.resolve().then(n.t.bind(n,9477,23)),Promise.resolve().then(n.t.bind(n,9345,23)),Promise.resolve().then(n.t.bind(n,2089,23)),Promise.resolve().then(n.t.bind(n,6577,23)),Promise.resolve().then(n.t.bind(n,1307,23))},1135:()=>{},4578:e=>{e.exports={header:"Header_header__MvnS2",headerContent:"Header_headerContent__zP2Kf",logo:"Header_logo__j7oID",tagline:"Header_tagline__olxb0",nav:"Header_nav__EE71E",navLink:"Header_navLink__Q_hTB",ctaButtons:"Header_ctaButtons__lpHrG",moreDropdown:"Header_moreDropdown__08Ox1",moreButton:"Header_moreButton__F_b_6",moreIcon:"Header_moreIcon__n_t7r",moreIconOpen:"Header_moreIconOpen__KNZVS",moreMenu:"Header_moreMenu__NRStY",fadeInDown:"Header_fadeInDown__v84oq",moreMenuItem:"Header_moreMenuItem__Peu9y",mobileMenuButton:"Header_mobileMenuButton__mHqbR",hamburger:"Header_hamburger___6DdG",mobileNav:"Header_mobileNav__J0kLN",mobileNavOpen:"Header_mobileNavOpen__gI4w0",mobileNavLink:"Header_mobileNavLink__b7nM_",mobileCta:"Header_mobileCta__3wkTH"}},4899:(e,s,n)=>{"use strict";n.d(s,{default:()=>d});var o=n(687),r=n(3210),a=n(5814),t=n.n(a),i=n(6189),l=n(4578),c=n.n(l);function d(){let[e,s]=(0,r.useState)(!1),[n,a]=(0,r.useState)(!1),[l,d]=(0,r.useState)(5),[m,h]=(0,r.useState)(!1),f=(0,i.usePathname)(),u=(0,r.useRef)(null),v=(0,r.useRef)(null),_=(0,r.useRef)(null),b=[{href:"/about",label:"About",show:"/about"!==f},{href:"/services",label:"Services",show:"/services"!==f},{href:"/bookings",label:"Bookings",show:"/bookings"!==f},{href:"/testimonials",label:"Testimonials",show:"/testimonials"!==f},{href:"/contact",label:"Contact",show:"/contact"!==f}].filter(e=>e.show),x=()=>{s(!e)},p=()=>{a(!n)},j=()=>{a(!1)};return(0,o.jsx)("header",{className:c().header,children:(0,o.jsxs)("div",{className:"container",children:[(0,o.jsxs)("div",{className:c().headerContent,children:[(0,o.jsxs)(t(),{href:"/",className:c().logo,children:[(0,o.jsx)("h1",{children:"Tuffside"}),(0,o.jsx)("span",{className:c().tagline,children:"Automotive Garage"})]}),(0,o.jsxs)("nav",{className:c().nav,ref:u,children:[b.slice(0,l).map(e=>(0,o.jsx)(t(),{href:e.href,className:c().navLink,children:e.label},e.href)),!m&&l<b.length&&(0,o.jsxs)("div",{className:c().moreDropdown,children:[(0,o.jsxs)("button",{ref:v,className:c().moreButton,onClick:p,onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),p())},"aria-expanded":n,"aria-haspopup":"true","aria-label":"More navigation options",children:[(0,o.jsx)("span",{children:"More"}),(0,o.jsx)("svg",{className:`${c().moreIcon} ${n?c().moreIconOpen:""}`,width:"16",height:"16",viewBox:"0 0 16 16",fill:"currentColor",children:(0,o.jsx)("path",{d:"M8 12l-4-4h8l-4 4z"})})]}),n&&(0,o.jsx)("div",{ref:_,className:c().moreMenu,onKeyDown:e=>{"Escape"===e.key&&(a(!1),v.current?.focus())},role:"menu","aria-label":"Additional navigation options",children:b.slice(l).map(e=>(0,o.jsx)(t(),{href:e.href,className:c().moreMenuItem,onClick:j,role:"menuitem",children:e.label},e.href))})]})]}),(0,o.jsxs)("div",{className:c().ctaButtons,children:[(0,o.jsx)("a",{href:"tel:+18683357440",className:"btn btn-primary","aria-label":"Call Tuffside Automotive Garage",children:"Call Now"}),(0,o.jsx)("a",{href:"https://wa.me/18683357440",target:"_blank",rel:"noopener noreferrer",className:"btn","aria-label":"Message us on WhatsApp",children:"WhatsApp"})]}),(0,o.jsxs)("button",{className:c().mobileMenuButton,onClick:x,"aria-label":"Toggle mobile menu","aria-expanded":e,children:[(0,o.jsx)("span",{className:c().hamburger}),(0,o.jsx)("span",{className:c().hamburger}),(0,o.jsx)("span",{className:c().hamburger})]})]}),(0,o.jsxs)("nav",{className:`${c().mobileNav} ${e?c().mobileNavOpen:""}`,children:["/"!==f&&(0,o.jsx)(t(),{href:"/",className:c().mobileNavLink,onClick:x,children:"Home"}),"/about"!==f&&(0,o.jsx)(t(),{href:"/about",className:c().mobileNavLink,onClick:x,children:"About"}),"/services"!==f&&(0,o.jsx)(t(),{href:"/services",className:c().mobileNavLink,onClick:x,children:"Services"}),"/bookings"!==f&&(0,o.jsx)(t(),{href:"/bookings",className:c().mobileNavLink,onClick:x,children:"Bookings"}),"/testimonials"!==f&&(0,o.jsx)(t(),{href:"/testimonials",className:c().mobileNavLink,onClick:x,children:"Testimonials"}),"/contact"!==f&&(0,o.jsx)(t(),{href:"/contact",className:c().mobileNavLink,onClick:x,children:"Contact"}),(0,o.jsxs)("div",{className:c().mobileCta,children:[(0,o.jsx)("a",{href:"tel:+18683357440",className:"btn btn-primary btn-large",onClick:x,children:"Call Now"}),(0,o.jsx)("a",{href:"https://wa.me/18683357440",target:"_blank",rel:"noopener noreferrer",className:"btn btn-large",onClick:x,children:"WhatsApp"})]})]})]})})}},5413:(e,s,n)=>{Promise.resolve().then(n.t.bind(n,4536,23)),Promise.resolve().then(n.bind(n,9741))},7269:(e,s,n)=>{Promise.resolve().then(n.t.bind(n,5814,23)),Promise.resolve().then(n.bind(n,4899))},7779:(e,s,n)=>{"use strict";n.r(s),n.d(s,{default:()=>v,metadata:()=>u});var o=n(7413),r=n(5461),a=n.n(r),t=n(681),i=n.n(t);n(1135);var l=n(9741),c=n(4536),d=n.n(c),m=n(9768),h=n.n(m);function f(){let e=new Date().getFullYear();return(0,o.jsx)("footer",{className:h().footer,children:(0,o.jsxs)("div",{className:"container",children:[(0,o.jsxs)("div",{className:h().footerContent,children:[(0,o.jsxs)("div",{className:h().footerSection,children:[(0,o.jsx)("h3",{children:"Tuffside Automotive"}),(0,o.jsx)("p",{className:h().tagline,children:"Built to Last. Tuned to Perform."}),(0,o.jsx)("p",{children:"Professional automotive repair and maintenance services in Trinidad."})]}),(0,o.jsxs)("div",{className:h().footerSection,children:[(0,o.jsx)("h4",{children:"Quick Links"}),(0,o.jsxs)("nav",{className:h().footerNav,children:[(0,o.jsx)(d(),{href:"/",className:h().footerLink,children:"Home"}),(0,o.jsx)(d(),{href:"/about",className:h().footerLink,children:"About"}),(0,o.jsx)(d(),{href:"/services",className:h().footerLink,children:"Services"}),(0,o.jsx)(d(),{href:"/bookings",className:h().footerLink,children:"Bookings"}),(0,o.jsx)(d(),{href:"/testimonials",className:h().footerLink,children:"Testimonials"}),(0,o.jsx)(d(),{href:"/contact",className:h().footerLink,children:"Contact"})]})]}),(0,o.jsxs)("div",{className:h().footerSection,children:[(0,o.jsx)("h4",{children:"Contact Us"}),(0,o.jsxs)("div",{className:h().contactInfo,children:[(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"Phone:"})," ",(0,o.jsx)("a",{href:"tel:+18683357440",className:h().contactLink,children:"****** 335-7440"})]}),(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"Email:"})," ",(0,o.jsx)("a",{href:"mailto:<EMAIL>",className:h().contactLink,children:"<EMAIL>"})]}),(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"WhatsApp:"})," ",(0,o.jsx)("a",{href:"https://wa.me/18683357440",target:"_blank",rel:"noopener noreferrer",className:h().contactLink,children:"Message Us"})]})]})]}),(0,o.jsxs)("div",{className:h().footerSection,children:[(0,o.jsx)("h4",{children:"Our Services"}),(0,o.jsxs)("ul",{className:h().servicesList,children:[(0,o.jsx)("li",{children:"Engine Diagnostics"}),(0,o.jsx)("li",{children:"Diesel Repair"}),(0,o.jsx)("li",{children:"Engine Tuning"}),(0,o.jsx)("li",{children:"Suspension Work"}),(0,o.jsx)("li",{children:"General Maintenance"}),(0,o.jsx)("li",{children:"Emergency Services"})]})]})]}),(0,o.jsx)("div",{className:h().footerBottom,children:(0,o.jsxs)("div",{className:h().footerBottomContent,children:[(0,o.jsxs)("p",{children:["\xa9 ",e," Tuffside Automotive Garage. All rights reserved."]}),(0,o.jsxs)("div",{className:h().footerCta,children:[(0,o.jsx)("a",{href:"tel:+18683357440",className:"btn btn-primary","aria-label":"Call Tuffside Automotive Garage",children:"Call Now"}),(0,o.jsx)("a",{href:"https://wa.me/18683357440",target:"_blank",rel:"noopener noreferrer",className:"btn","aria-label":"Message us on WhatsApp",children:"WhatsApp"})]})]})})]})})}let u={title:"Tuffside Automotive Garage - Built to Last. Tuned to Perform.",description:"Professional automotive repair and maintenance services in Trinidad. Expert diagnostics, diesel repair, engine tuning, and suspension work. Call ****** 335-7440 or book online.",keywords:"automotive repair, car service, diesel repair, engine tuning, suspension, Trinidad garage, vehicle maintenance",openGraph:{title:"Tuffside Automotive Garage",description:"Professional automotive repair and maintenance services in Trinidad",type:"website"}};function v({children:e}){return(0,o.jsx)("html",{lang:"en",children:(0,o.jsxs)("body",{className:`${a().variable} ${i().variable}`,children:[(0,o.jsx)(l.default,{}),(0,o.jsx)("main",{children:e}),(0,o.jsx)(f,{})]})})}},7784:(e,s,n)=>{Promise.resolve().then(n.t.bind(n,6346,23)),Promise.resolve().then(n.t.bind(n,7924,23)),Promise.resolve().then(n.t.bind(n,5656,23)),Promise.resolve().then(n.t.bind(n,99,23)),Promise.resolve().then(n.t.bind(n,8243,23)),Promise.resolve().then(n.t.bind(n,8827,23)),Promise.resolve().then(n.t.bind(n,2763,23)),Promise.resolve().then(n.t.bind(n,7173,23))},9741:(e,s,n)=>{"use strict";n.d(s,{default:()=>o});let o=(0,n(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Workspace/APP/Tuffside/src/components/Header.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Workspace/APP/Tuffside/src/components/Header.js","default")},9768:e=>{e.exports={footer:"Footer_footer__UaG1g",footerContent:"Footer_footerContent__nZq4z",footerSection:"Footer_footerSection__oCSkQ",tagline:"Footer_tagline__EtMBR",footerNav:"Footer_footerNav__favhI",footerLink:"Footer_footerLink__twEJY",contactInfo:"Footer_contactInfo__B2wyf",contactLink:"Footer_contactLink__ZkhIE",servicesList:"Footer_servicesList__s5l0g",footerBottom:"Footer_footerBottom__jFaYu",footerBottomContent:"Footer_footerBottomContent__PaWOB",footerCta:"Footer_footerCta__Iw101",btn:"Footer_btn__9IAlu"}}};