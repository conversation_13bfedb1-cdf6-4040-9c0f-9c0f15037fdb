{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/CTAButtons.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"btn\": \"CTAButtons-module__AIDVgW__btn\",\n  \"ctaContainer\": \"CTAButtons-module__AIDVgW__ctaContainer\",\n  \"horizontal\": \"CTAButtons-module__AIDVgW__horizontal\",\n  \"small\": \"CTAButtons-module__AIDVgW__small\",\n  \"vertical\": \"CTAButtons-module__AIDVgW__vertical\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/src/components/CTAButtons.js"], "sourcesContent": ["import styles from './CTAButtons.module.css';\n\nexport default function CTAButtons({ \n  layout = 'horizontal', \n  size = 'default',\n  showBooking = true,\n  className = '' \n}) {\n  const containerClass = `${styles.ctaContainer} ${styles[layout]} ${styles[size]} ${className}`;\n\n  return (\n    <div className={containerClass}>\n      {showBooking && (\n        <a \n          href=\"/bookings\" \n          className=\"btn btn-primary btn-large\"\n          aria-label=\"Book an appointment\"\n        >\n          Book Appointment\n        </a>\n      )}\n      \n      <a \n        href=\"tel:+18683357440\" \n        className=\"btn btn-large\"\n        aria-label=\"Call Tuffside Automotive Garage\"\n      >\n        📞 Call Now\n      </a>\n      \n      <a \n        href=\"https://wa.me/18683357440\" \n        target=\"_blank\" \n        rel=\"noopener noreferrer\"\n        className=\"btn btn-large\"\n        aria-label=\"Message us on WhatsApp\"\n      >\n        💬 WhatsApp\n      </a>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS,WAAW,EACjC,SAAS,YAAY,EACrB,OAAO,SAAS,EAChB,cAAc,IAAI,EAClB,YAAY,EAAE,EACf;IACC,MAAM,iBAAiB,GAAG,2IAAA,CAAA,UAAM,CAAC,YAAY,CAAC,CAAC,EAAE,2IAAA,CAAA,UAAM,CAAC,OAAO,CAAC,CAAC,EAAE,2IAAA,CAAA,UAAM,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;IAE9F,qBACE,8OAAC;QAAI,WAAW;;YACb,6BACC,8OAAC;gBACC,MAAK;gBACL,WAAU;gBACV,cAAW;0BACZ;;;;;;0BAKH,8OAAC;gBACC,MAAK;gBACL,WAAU;gBACV,cAAW;0BACZ;;;;;;0BAID,8OAAC;gBACC,MAAK;gBACL,QAAO;gBACP,KAAI;gBACJ,WAAU;gBACV,cAAW;0BACZ;;;;;;;;;;;;AAKP", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/services/page.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"benefitCard\": \"page-module__VKYseq__benefitCard\",\n  \"cta\": \"page-module__VKYseq__cta\",\n  \"ctaContent\": \"page-module__VKYseq__ctaContent\",\n  \"hero\": \"page-module__VKYseq__hero\",\n  \"heroContent\": \"page-module__VKYseq__heroContent\",\n  \"heroDescription\": \"page-module__VKYseq__heroDescription\",\n  \"serviceCard\": \"page-module__VKYseq__serviceCard\",\n  \"serviceDescription\": \"page-module__VKYseq__serviceDescription\",\n  \"serviceFeatures\": \"page-module__VKYseq__serviceFeatures\",\n  \"serviceHeader\": \"page-module__VKYseq__serviceHeader\",\n  \"serviceIcon\": \"page-module__VKYseq__serviceIcon\",\n  \"servicesGrid\": \"page-module__VKYseq__servicesGrid\",\n  \"servicesPage\": \"page-module__VKYseq__servicesPage\",\n  \"whyChoose\": \"page-module__VKYseq__whyChoose\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/src/app/services/page.js"], "sourcesContent": ["import CTAButtons from '@/components/CTAButtons';\nimport styles from './page.module.css';\n\nexport const metadata = {\n  title: \"Automotive Services - Tuffside Garage Trinidad\",\n  description: \"Complete automotive services including engine diagnostics, diesel repair, engine tuning, suspension work, and emergency services in Trinidad.\",\n};\n\nexport default function Services() {\n  const services = [\n    {\n      icon: \"🔧\",\n      title: \"Engine Diagnostics\",\n      description: \"Advanced computer diagnostics to identify engine problems quickly and accurately. We use state-of-the-art equipment to pinpoint issues and provide detailed reports.\",\n      features: [\"OBD-II scanning\", \"Borescope inspection\", \"Fault code interpretation\"]\n    },\n    {\n      icon: \"⚙️\",\n      title: \"Diesel Repair\",\n      description: \"Specialised diesel engine repair and maintenance for trucks, buses, and heavy machinery. Our expertise covers all diesel systems and components.\",\n      features: [\"Fuel injection repair\", \"Turbocharger service\", \"Diesel engine rebuilds\", \"Emission system repair\"]\n    },\n    {\n      icon: \"🏁\",\n      title: \"Engine Tuning\",\n      description: \"Performance tuning and optimisation to maximize your engine's potential. From economy tuning to performance enhancement.\",\n      features: [\"Fuel efficiency optimization\", \"Power enhancement\", \"Engine modification\"]\n    },\n    {\n      icon: \"🔩\",\n      title: \"Suspension Work\",\n      description: \"Complete suspension system repair and maintenance to ensure a smooth, safe ride and proper vehicle handling.\",\n      features: [\"Shock absorber replacement\", \"Spring repair\", \"Alignment services\", \"Suspension diagnostics\", \"Ride height modifications\"]\n    },\n    {\n      icon: \"🚘\",\n      title: \"Body Work and Replacement\",\n      description: \"Full bodywork services including dent removal, panel replacement, sanding, priming, and painting. We restore vehicles to their best condition after accidents, wear, or custom requests.\",\n      features: [\"Panel replacement\", \"Dent repair\", \"Sanding and priming\", \"Paint respray\", \"Rust treatment\"]\n    },\n    {\n      icon: \"🛠️\",\n      title: \"General Maintenance\",\n      description: \"Regular maintenance services to keep your vehicle running smoothly and prevent costly repairs down the road.\",\n      features: [\"Oil changes\", \"Filter replacements\", \"Brake service\", \"Tyre rotation\"]\n    },\n    {\n      icon: \"🚨\",\n      title: \"Emergency Services\",\n      description: \"24/7 emergency call-out services for breakdowns, urgent repairs, and vehicle recovery across Trinidad.\",\n      features: [\"Roadside assistance\", \"Jump starts\", \"Tyre changes\", \"Emergency towing\"]\n    },\n    {\n      icon: \"🚛\",\n      title: \"Commercial Vehicles\",\n      description: \"Specialized services for commercial fleets, trucks, and heavy-duty vehicles with flexible scheduling.\",\n      features: [\"Fleet maintenance\", \"Commercial diagnostics\", \"Heavy-duty repairs\", \"Preventive maintenance\"]\n    }\n  ];\n\n  return (\n    <div className={styles.servicesPage}>\n      {/* Hero Section */}\n      <section className={styles.hero}>\n        <div className=\"container\">\n          <div className={styles.heroContent}>\n            <h1>Our Services</h1>\n            <p className={styles.heroDescription}>\n              Comprehensive automotive repair and maintenance services designed to keep \n              your vehicle running at its best. From routine maintenance to complex repairs, \n              we've got you covered.\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* Services Grid */}\n      <section className={styles.servicesGrid}>\n        <div className=\"container\">\n          <div className=\"grid grid-2\">\n            {services.map((service, index) => (\n              <div key={index} className={styles.serviceCard}>\n                <div className={styles.serviceHeader}>\n                  <div className={styles.serviceIcon}>{service.icon}</div>\n                  <h3>{service.title}</h3>\n                </div>\n                <p className={styles.serviceDescription}>{service.description}</p>\n                <ul className={styles.serviceFeatures}>\n                  {service.features.map((feature, featureIndex) => (\n                    <li key={featureIndex}>✓ {feature}</li>\n                  ))}\n                </ul>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Why Choose Our Services */}\n      <section className={styles.whyChoose}>\n        <div className=\"container\">\n          <h2 className=\"text-center mb-lg\">Why Choose Our Services?</h2>\n          <div className=\"grid grid-3\">\n            <div className={styles.benefitCard}>\n              <h4>Expert Technicians</h4>\n              <p>Our certified mechanics have years of experience working on all makes and models.</p>\n            </div>\n            <div className={styles.benefitCard}>\n              <h4>Quality Parts</h4>\n              <p>We use only high-quality used and new parts and to ensure lasting repairs at a fair price.</p>\n            </div>\n            <div className={styles.benefitCard}>\n              <h4>Fair Pricing</h4>\n              <p>Transparent, competitive pricing with no hidden fees or surprises.</p>\n            </div>\n            <div className={styles.benefitCard}>\n              <h4>Quick Service</h4>\n              <p>Efficient service to get you back on the road as quickly as possible.</p>\n            </div>\n            <div className={styles.benefitCard}>\n              <h4>Trusted Partners</h4>\n              <p>We partner with trusted machinists and bodywork experts to offer a full compliment of vehicular services.</p>\n            </div>\n            <div className={styles.benefitCard}>\n              <h4>Emergency Support</h4>\n              <p>24/7 emergency services when you need help the most.</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Call to Action */}\n      <section className={styles.cta}>\n        <div className=\"container\">\n          <div className={styles.ctaContent}>\n            <h2>Ready to Service Your Vehicle?</h2>\n            <p>\n              Don't wait for small problems to become big ones. Contact us today to \n              schedule your service or get a free quote.\n            </p>\n            <CTAButtons layout=\"horizontal\" size=\"large\" />\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;AACf;AAEe,SAAS;IACtB,MAAM,WAAW;QACf;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAmB;gBAAwB;aAA4B;QACpF;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAyB;gBAAwB;gBAA0B;aAAyB;QACjH;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAgC;gBAAqB;aAAsB;QACxF;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAA8B;gBAAiB;gBAAsB;gBAA0B;aAA4B;QACxI;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAqB;gBAAe;gBAAuB;gBAAiB;aAAiB;QAC1G;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAe;gBAAuB;gBAAiB;aAAgB;QACpF;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAuB;gBAAe;gBAAgB;aAAmB;QACtF;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAqB;gBAA0B;gBAAsB;aAAyB;QAC3G;KACD;IAED,qBACE,8OAAC;QAAI,WAAW,0IAAA,CAAA,UAAM,CAAC,YAAY;;0BAEjC,8OAAC;gBAAQ,WAAW,0IAAA,CAAA,UAAM,CAAC,IAAI;0BAC7B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAW,0IAAA,CAAA,UAAM,CAAC,WAAW;;0CAChC,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;gCAAE,WAAW,0IAAA,CAAA,UAAM,CAAC,eAAe;0CAAE;;;;;;;;;;;;;;;;;;;;;;0BAU5C,8OAAC;gBAAQ,WAAW,0IAAA,CAAA,UAAM,CAAC,YAAY;0BACrC,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;gCAAgB,WAAW,0IAAA,CAAA,UAAM,CAAC,WAAW;;kDAC5C,8OAAC;wCAAI,WAAW,0IAAA,CAAA,UAAM,CAAC,aAAa;;0DAClC,8OAAC;gDAAI,WAAW,0IAAA,CAAA,UAAM,CAAC,WAAW;0DAAG,QAAQ,IAAI;;;;;;0DACjD,8OAAC;0DAAI,QAAQ,KAAK;;;;;;;;;;;;kDAEpB,8OAAC;wCAAE,WAAW,0IAAA,CAAA,UAAM,CAAC,kBAAkB;kDAAG,QAAQ,WAAW;;;;;;kDAC7D,8OAAC;wCAAG,WAAW,0IAAA,CAAA,UAAM,CAAC,eAAe;kDAClC,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,8OAAC;;oDAAsB;oDAAG;;+CAAjB;;;;;;;;;;;+BARL;;;;;;;;;;;;;;;;;;;;0BAkBlB,8OAAC;gBAAQ,WAAW,0IAAA,CAAA,UAAM,CAAC,SAAS;0BAClC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoB;;;;;;sCAClC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAW,0IAAA,CAAA,UAAM,CAAC,WAAW;;sDAChC,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAE;;;;;;;;;;;;8CAEL,8OAAC;oCAAI,WAAW,0IAAA,CAAA,UAAM,CAAC,WAAW;;sDAChC,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAE;;;;;;;;;;;;8CAEL,8OAAC;oCAAI,WAAW,0IAAA,CAAA,UAAM,CAAC,WAAW;;sDAChC,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAE;;;;;;;;;;;;8CAEL,8OAAC;oCAAI,WAAW,0IAAA,CAAA,UAAM,CAAC,WAAW;;sDAChC,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAE;;;;;;;;;;;;8CAEL,8OAAC;oCAAI,WAAW,0IAAA,CAAA,UAAM,CAAC,WAAW;;sDAChC,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAE;;;;;;;;;;;;8CAEL,8OAAC;oCAAI,WAAW,0IAAA,CAAA,UAAM,CAAC,WAAW;;sDAChC,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOX,8OAAC;gBAAQ,WAAW,0IAAA,CAAA,UAAM,CAAC,GAAG;0BAC5B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAW,0IAAA,CAAA,UAAM,CAAC,UAAU;;0CAC/B,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;0CAIH,8OAAC,+HAAA,CAAA,UAAU;gCAAC,QAAO;gCAAa,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjD", "debugId": null}}, {"offset": {"line": 596, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 634, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,GAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}