{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/testimonials/page.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"cardContent\": \"page-module__hPV2JG__cardContent\",\n  \"cardFooter\": \"page-module__hPV2JG__cardFooter\",\n  \"cardHeader\": \"page-module__hPV2JG__cardHeader\",\n  \"cta\": \"page-module__hPV2JG__cta\",\n  \"ctaButtons\": \"page-module__hPV2JG__ctaButtons\",\n  \"ctaContent\": \"page-module__hPV2JG__ctaContent\",\n  \"customerInfo\": \"page-module__hPV2JG__customerInfo\",\n  \"hero\": \"page-module__hPV2JG__hero\",\n  \"heroContent\": \"page-module__hPV2JG__heroContent\",\n  \"heroDescription\": \"page-module__hPV2JG__heroDescription\",\n  \"location\": \"page-module__hPV2JG__location\",\n  \"rating\": \"page-module__hPV2JG__rating\",\n  \"review\": \"page-module__hPV2JG__review\",\n  \"serviceInfo\": \"page-module__hPV2JG__serviceInfo\",\n  \"serviceTicket\": \"page-module__hPV2JG__serviceTicket\",\n  \"serviceType\": \"page-module__hPV2JG__serviceType\",\n  \"statCard\": \"page-module__hPV2JG__statCard\",\n  \"stats\": \"page-module__hPV2JG__stats\",\n  \"statsGrid\": \"page-module__hPV2JG__statsGrid\",\n  \"testimonialCard\": \"page-module__hPV2JG__testimonialCard\",\n  \"testimonials\": \"page-module__hPV2JG__testimonials\",\n  \"testimonialsGrid\": \"page-module__hPV2JG__testimonialsGrid\",\n  \"testimonialsPage\": \"page-module__hPV2JG__testimonialsPage\",\n  \"ticketDate\": \"page-module__hPV2JG__ticketDate\",\n  \"ticketId\": \"page-module__hPV2JG__ticketId\",\n  \"vehicle\": \"page-module__hPV2JG__vehicle\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/src/app/testimonials/page.js"], "sourcesContent": ["import styles from './page.module.css';\n\nexport const metadata = {\n  title: \"Customer Testimonials - Tuffside Automotive Garage\",\n  description: \"Read what our customers say about Tuffside Automotive Garage. Real reviews from satisfied customers in Trinidad.\",\n};\n\nexport default function Testimonials() {\n  const testimonials = [\n    {\n      id: \"TS001\",\n      name: \"<PERSON> T.\",\n      vehicle: \"2018 Toyota Hilux\",\n      service: \"Diesel Engine Repair\",\n      rating: 5,\n      date: \"November 2024\",\n      review: \"My Hilux was losing power and making strange noises. The team at Tuffside diagnosed the problem quickly and had it fixed the same day. Honest pricing and excellent work. I'll definitely be back for my next service.\",\n      location: \"Port of Spain\"\n    },\n    {\n      id: \"TS002\", \n      name: \"<PERSON>\",\n      vehicle: \"2020 Honda Civic\",\n      service: \"General Servicing\",\n      rating: 5,\n      date: \"October 2024\",\n      review: \"Professional service from start to finish. They explained everything they were doing and why. My car runs like new again. The staff is friendly and the prices are very reasonable. Highly recommended!\",\n      location: \"Oropouche\"\n    },\n    {\n      id: \"TS003\",\n      name: \"<PERSON>\",\n      vehicle: \"2016 Nissan Frontier\",\n      service: \"Emergency Roadside\",\n      rating: 5,\n      date: \"October 2024\",\n      review: \"Broke down on the highway at 8 PM. Called Tuffside and they were there within 30 minutes! Fixed the alternator issue on the spot. Saved my weekend trip. These guys are lifesavers!\",\n      location: \"Chaguanas\"\n    },\n    {\n      id: \"TS004\",\n      name: \"Jennifer W.\",\n      vehicle: \"2019 Suzuki Swift\",\n      service: \"Brake Repair\",\n      rating: 5,\n      date: \"September 2024\",\n      review: \"Had brake problems and was worried about safety. Tuffside took care of everything quickly and thoroughly. They even showed me the old parts and explained what was wrong. Great customer service!\",\n      location: \"Siparia\"\n    },\n    {\n      id: \"TS005\",\n      name: \"Robert S.\",\n      vehicle: \"2017 Ford Ranger\",\n      service: \"Engine Tuning\",\n      rating: 5,\n      date: \"September 2024\",\n      review: \"Wanted to improve my truck's performance and fuel economy. The engine tuning service exceeded my expectations. Better power, better mileage, and the truck feels brand new. Worth every dollar!\",\n      location: \"Couva\"\n    },\n    {\n      id: \"TS006\",\n      name: \"Lisa R.\",\n      vehicle: \"2015 Hyundai Elantra\",\n      service: \"Electrical Diagnostics\",\n      rating: 5,\n      date: \"August 2024\",\n      review: \"Had mysterious electrical issues that other garages couldn't figure out. Tuffside found the problem in no time and fixed it properly. Finally, my car is reliable again. Thank you!\",\n      location: \"Marabella\"\n    }\n  ];\n\n  const renderStars = (rating) => {\n    return '★'.repeat(rating) + '☆'.repeat(5 - rating);\n  };\n\n  return (\n    <div className={styles.testimonialsPage}>\n      {/* Hero Section */}\n      <section className={styles.hero}>\n        <div className=\"container\">\n          <div className={styles.heroContent}>\n            <h1>Customer Testimonials</h1>\n            <p className={styles.heroDescription}>\n              Don't just take our word for it - hear what our customers have to say \n              about their experience with Tuffside Automotive Garage.\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* Stats Section */}\n      <section className={styles.stats}>\n        <div className=\"container\">\n          <div className={styles.statsGrid}>\n            <div className={styles.statCard}>\n              <h3>500+</h3>\n              <p>Happy Customers</p>\n            </div>\n            <div className={styles.statCard}>\n              <h3>98%</h3>\n              <p>Satisfaction Rate</p>\n            </div>\n            <div className={styles.statCard}>\n              <h3>4.9/5</h3>\n              <p>Average Rating</p>\n            </div>\n            <div className={styles.statCard}>\n              <h3>10+</h3>\n              <p>Years Experience</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Testimonials Grid */}\n      <section className={styles.testimonials}>\n        <div className=\"container\">\n          <div className={styles.testimonialsGrid}>\n            {testimonials.map((testimonial) => (\n              <div key={testimonial.id} className={styles.testimonialCard}>\n                <div className={styles.cardHeader}>\n                  <div className={styles.serviceTicket}>\n                    <span className={styles.ticketId}>#{testimonial.id}</span>\n                    <span className={styles.ticketDate}>{testimonial.date}</span>\n                  </div>\n                  <div className={styles.rating}>\n                    {renderStars(testimonial.rating)}\n                  </div>\n                </div>\n                \n                <div className={styles.cardContent}>\n                  <blockquote className={styles.review}>\n                    \"{testimonial.review}\"\n                  </blockquote>\n                </div>\n                \n                <div className={styles.cardFooter}>\n                  <div className={styles.customerInfo}>\n                    <h4>{testimonial.name}</h4>\n                    <p className={styles.vehicle}>{testimonial.vehicle}</p>\n                    <p className={styles.location}>{testimonial.location}</p>\n                  </div>\n                  <div className={styles.serviceInfo}>\n                    <span className={styles.serviceType}>{testimonial.service}</span>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Call to Action */}\n      <section className={styles.cta}>\n        <div className=\"container\">\n          <div className={styles.ctaContent}>\n            <h2>Ready to Join Our Happy Customers?</h2>\n            <p>\n              Experience the Tuffside difference for yourself. Book your service today \n              and see why our customers keep coming back.\n            </p>\n            <div className={styles.ctaButtons}>\n              <a href=\"/bookings\" className=\"btn btn-primary btn-large\">\n                Book Service\n              </a>\n              <a href=\"tel:+18683357440\" className=\"btn btn-large\">\n                Call Now\n              </a>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;AACf;AAEe,SAAS;IACtB,MAAM,eAAe;QACnB;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,SAAS;YACT,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,SAAS;YACT,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,SAAS;YACT,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,SAAS;YACT,QAAQ;YACR,MAAM;YACN,QAAQ;YAC<PERSON>,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,SAAS;YACT,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,SAAS;YACT,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;KACD;IAED,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,IAAI;IAC7C;IAEA,qBACE,8OAAC;QAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,gBAAgB;;0BAErC,8OAAC;gBAAQ,WAAW,8IAAA,CAAA,UAAM,CAAC,IAAI;0BAC7B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,WAAW;;0CAChC,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;gCAAE,WAAW,8IAAA,CAAA,UAAM,CAAC,eAAe;0CAAE;;;;;;;;;;;;;;;;;;;;;;0BAS5C,8OAAC;gBAAQ,WAAW,8IAAA,CAAA,UAAM,CAAC,KAAK;0BAC9B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,SAAS;;0CAC9B,8OAAC;gCAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,QAAQ;;kDAC7B,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAE;;;;;;;;;;;;0CAEL,8OAAC;gCAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,QAAQ;;kDAC7B,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAE;;;;;;;;;;;;0CAEL,8OAAC;gCAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,QAAQ;;kDAC7B,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAE;;;;;;;;;;;;0CAEL,8OAAC;gCAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,QAAQ;;kDAC7B,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOX,8OAAC;gBAAQ,WAAW,8IAAA,CAAA,UAAM,CAAC,YAAY;0BACrC,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,gBAAgB;kCACpC,aAAa,GAAG,CAAC,CAAC,4BACjB,8OAAC;gCAAyB,WAAW,8IAAA,CAAA,UAAM,CAAC,eAAe;;kDACzD,8OAAC;wCAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,UAAU;;0DAC/B,8OAAC;gDAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,aAAa;;kEAClC,8OAAC;wDAAK,WAAW,8IAAA,CAAA,UAAM,CAAC,QAAQ;;4DAAE;4DAAE,YAAY,EAAE;;;;;;;kEAClD,8OAAC;wDAAK,WAAW,8IAAA,CAAA,UAAM,CAAC,UAAU;kEAAG,YAAY,IAAI;;;;;;;;;;;;0DAEvD,8OAAC;gDAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,MAAM;0DAC1B,YAAY,YAAY,MAAM;;;;;;;;;;;;kDAInC,8OAAC;wCAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,WAAW;kDAChC,cAAA,8OAAC;4CAAW,WAAW,8IAAA,CAAA,UAAM,CAAC,MAAM;;gDAAE;gDAClC,YAAY,MAAM;gDAAC;;;;;;;;;;;;kDAIzB,8OAAC;wCAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,UAAU;;0DAC/B,8OAAC;gDAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,YAAY;;kEACjC,8OAAC;kEAAI,YAAY,IAAI;;;;;;kEACrB,8OAAC;wDAAE,WAAW,8IAAA,CAAA,UAAM,CAAC,OAAO;kEAAG,YAAY,OAAO;;;;;;kEAClD,8OAAC;wDAAE,WAAW,8IAAA,CAAA,UAAM,CAAC,QAAQ;kEAAG,YAAY,QAAQ;;;;;;;;;;;;0DAEtD,8OAAC;gDAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,WAAW;0DAChC,cAAA,8OAAC;oDAAK,WAAW,8IAAA,CAAA,UAAM,CAAC,WAAW;8DAAG,YAAY,OAAO;;;;;;;;;;;;;;;;;;+BAxBrD,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;0BAkChC,8OAAC;gBAAQ,WAAW,8IAAA,CAAA,UAAM,CAAC,GAAG;0BAC5B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,UAAU;;0CAC/B,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;0CAIH,8OAAC;gCAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,UAAU;;kDAC/B,8OAAC;wCAAE,MAAK;wCAAY,WAAU;kDAA4B;;;;;;kDAG1D,8OAAC;wCAAE,MAAK;wCAAmB,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnE", "debugId": null}}, {"offset": {"line": 546, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 584, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA,eAAwB;YAAA;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}