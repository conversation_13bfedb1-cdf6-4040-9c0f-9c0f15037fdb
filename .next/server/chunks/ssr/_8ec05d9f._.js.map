{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/BookingCard.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"bookingButton\": \"BookingCard-module__CVpzLq__bookingButton\",\n  \"bookingCard\": \"BookingCard-module__CVpzLq__bookingCard\",\n  \"cardContent\": \"BookingCard-module__CVpzLq__cardContent\",\n  \"cardFooter\": \"BookingCard-module__CVpzLq__cardFooter\",\n  \"cardHeader\": \"BookingCard-module__CVpzLq__cardHeader\",\n  \"cardIcon\": \"BookingCard-module__CVpzLq__cardIcon\",\n  \"cardTitle\": \"BookingCard-module__CVpzLq__cardTitle\",\n  \"description\": \"BookingCard-module__CVpzLq__description\",\n  \"duration\": \"BookingCard-module__CVpzLq__duration\",\n  \"pulse\": \"BookingCard-module__CVpzLq__pulse\",\n  \"urgent\": \"BookingCard-module__CVpzLq__urgent\",\n  \"urgentBadge\": \"BookingCard-module__CVpzLq__urgentBadge\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/src/components/BookingCard.js"], "sourcesContent": ["import styles from './BookingCard.module.css';\n\nexport default function BookingCard({ \n  title, \n  duration, \n  description, \n  bookingUrl, \n  icon, \n  urgent = false \n}) {\n  const cardClass = `${styles.bookingCard} ${urgent ? styles.urgent : ''}`;\n\n  return (\n    <div className={cardClass}>\n      <div className={styles.cardHeader}>\n        <div className={styles.cardIcon}>{icon}</div>\n        <div className={styles.cardTitle}>\n          <h3>{title}</h3>\n          <span className={styles.duration}>Duration: {duration}</span>\n        </div>\n      </div>\n      \n      <div className={styles.cardContent}>\n        <p className={styles.description}>{description}</p>\n      </div>\n      \n      <div className={styles.cardFooter}>\n        <a \n          href={bookingUrl}\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n          className={`btn ${urgent ? 'btn-primary' : ''} btn-large ${styles.bookingButton}`}\n        >\n          Book Now\n        </a>\n      </div>\n      \n      {urgent && (\n        <div className={styles.urgentBadge}>\n          Emergency Service\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS,YAAY,EAClC,KAAK,EACL,QAAQ,EACR,WAAW,EACX,UAAU,EACV,IAAI,EACJ,SAAS,KAAK,EACf;IACC,MAAM,YAAY,GAAG,4IAAA,CAAA,UAAM,CAAC,WAAW,CAAC,CAAC,EAAE,SAAS,4IAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IAAI;IAExE,qBACE,8OAAC;QAAI,WAAW;;0BACd,8OAAC;gBAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,UAAU;;kCAC/B,8OAAC;wBAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,QAAQ;kCAAG;;;;;;kCAClC,8OAAC;wBAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,SAAS;;0CAC9B,8OAAC;0CAAI;;;;;;0CACL,8OAAC;gCAAK,WAAW,4IAAA,CAAA,UAAM,CAAC,QAAQ;;oCAAE;oCAAW;;;;;;;;;;;;;;;;;;;0BAIjD,8OAAC;gBAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,WAAW;0BAChC,cAAA,8OAAC;oBAAE,WAAW,4IAAA,CAAA,UAAM,CAAC,WAAW;8BAAG;;;;;;;;;;;0BAGrC,8OAAC;gBAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,UAAU;0BAC/B,cAAA,8OAAC;oBACC,MAAM;oBACN,QAAO;oBACP,KAAI;oBACJ,WAAW,CAAC,IAAI,EAAE,SAAS,gBAAgB,GAAG,WAAW,EAAE,4IAAA,CAAA,UAAM,CAAC,aAAa,EAAE;8BAClF;;;;;;;;;;;YAKF,wBACC,8OAAC;gBAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,WAAW;0BAAE;;;;;;;;;;;;AAM5C", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/bookings/page.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"bookingCards\": \"page-module__rNCILW__bookingCards\",\n  \"bookingInfo\": \"page-module__rNCILW__bookingInfo\",\n  \"bookingsPage\": \"page-module__rNCILW__bookingsPage\",\n  \"cardsGrid\": \"page-module__rNCILW__cardsGrid\",\n  \"contactInfo\": \"page-module__rNCILW__contactInfo\",\n  \"contactOptions\": \"page-module__rNCILW__contactOptions\",\n  \"emergency\": \"page-module__rNCILW__emergency\",\n  \"emergencyContent\": \"page-module__rNCILW__emergencyContent\",\n  \"hero\": \"page-module__rNC<PERSON>W__hero\",\n  \"heroContent\": \"page-module__rNC<PERSON>W__heroContent\",\n  \"heroDescription\": \"page-module__rNCILW__heroDescription\",\n  \"infoContent\": \"page-module__rNC<PERSON>W__infoContent\",\n  \"infoText\": \"page-module__rNC<PERSON>W__infoText\",\n  \"stepsList\": \"page-module__rNCILW__stepsList\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/src/app/bookings/page.js"], "sourcesContent": ["import BookingCard from '@/components/BookingCard';\nimport styles from './page.module.css';\n\nexport const metadata = {\n  title: \"Book a Service - Tuffside Automotive Garage\",\n  description: \"Book your automotive service online. Choose from emergency services, repairs, or general servicing. Easy online booking with TidyCal.\",\n};\n\nexport default function Bookings() {\n  const bookingTypes = [\n    {\n      title: \"Emergency or On-site Services\",\n      duration: \"1 hour\",\n      description: \"Stuck on the road or need help at your location? We offer call-outs for breakdowns, urgent repairs, and vehicle recovery. Whether it's a dead battery, a flat tyre, or something more involved, we're equipped to respond quickly and get you moving again.\",\n      bookingUrl: \"https://tidycal.com/1w7lwem/emergency-or-on-site-services\",\n      icon: \"🚨\",\n      urgent: true\n    },\n    {\n      title: \"Repairs\",\n      duration: \"15 minutes\",\n      description: \"Whether it's something minor or a bit more serious, we'll take a thorough look and get your vehicle back in shape. This includes engine diagnostics, suspension issues, brake repairs, electrical faults, and other mechanical concerns. Bring it in—we'll sort it out.\",\n      bookingUrl: \"https://tidycal.com/1w7lwem/repairs\",\n      icon: \"🔧\",\n      urgent: false\n    },\n    {\n      title: \"General Servicing\",\n      duration: \"2 hours\",\n      description: \"Keep your vehicle running smoothly with a routine check-up. This service covers a standard maintenance checklist including oil and filter changes, fluid top-ups, brake inspection, tyre rotation, battery health, and more. Ideal for regular upkeep and peace of mind.\",\n      bookingUrl: \"https://tidycal.com/1w7lwem/general-servicing\",\n      icon: \"🛠️\",\n      urgent: false\n    }\n  ];\n\n  return (\n    <div className={styles.bookingsPage}>\n      {/* Hero Section */}\n      <section className={styles.hero}>\n        <div className=\"container\">\n          <div className={styles.heroContent}>\n            <h1>Book a Service</h1>\n            <p className={styles.heroDescription}>\n              Choose the service that best fits your needs and book online. \n              We'll confirm your appointment and get your vehicle back in top shape.\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* Booking Cards */}\n      <section className={styles.bookingCards}>\n        <div className=\"container\">\n          <div className={styles.cardsGrid}>\n            {bookingTypes.map((booking, index) => (\n              <BookingCard\n                key={index}\n                title={booking.title}\n                duration={booking.duration}\n                description={booking.description}\n                bookingUrl={booking.bookingUrl}\n                icon={booking.icon}\n                urgent={booking.urgent}\n              />\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Booking Info */}\n      <section className={styles.bookingInfo}>\n        <div className=\"container\">\n          <div className={styles.infoContent}>\n            <div className={styles.infoText}>\n              <h2>How It Works</h2>\n              <ol className={styles.stepsList}>\n                <li>\n                  <strong>Choose Your Service:</strong> Select the type of service \n                  that best matches your needs from the options above.\n                </li>\n                <li>\n                  <strong>Pick Your Time:</strong> Choose a convenient date and time \n                  from our available slots.\n                </li>\n                <li>\n                  <strong>Provide Details:</strong> Tell us about your vehicle and \n                  any specific concerns you have.\n                </li>\n                <li>\n                  <strong>Confirmation:</strong> We'll confirm your booking and send \n                  you all the details you need.\n                </li>\n              </ol>\n            </div>\n            <div className={styles.contactInfo}>\n              <h3>Need Help Booking?</h3>\n              <p>\n                If you're not sure which service you need or prefer to book over \n                the phone, we're here to help.\n              </p>\n              <div className={styles.contactOptions}>\n                <a \n                  href=\"tel:+18683357440\" \n                  className=\"btn btn-primary\"\n                >\n                  📞 Call Us\n                </a>\n                <a \n                  href=\"https://wa.me/18683357440\" \n                  target=\"_blank\" \n                  rel=\"noopener noreferrer\"\n                  className=\"btn\"\n                >\n                  💬 WhatsApp\n                </a>\n                <a \n                  href=\"mailto:<EMAIL>\" \n                  className=\"btn\"\n                >\n                  ✉️ Email Us\n                </a>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Emergency Notice */}\n      <section className={styles.emergency}>\n        <div className=\"container\">\n          <div className={styles.emergencyContent}>\n            <h2>🚨 Emergency Breakdown?</h2>\n            <p>\n              If you're currently broken down or need immediate assistance, \n              don't wait for an appointment - call us now!\n            </p>\n            <a \n              href=\"tel:+18683357440\" \n              className=\"btn btn-primary btn-large\"\n            >\n              📞 Emergency Call: ****** 335-7440\n            </a>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;AACf;AAEe,SAAS;IACtB,MAAM,eAAe;QACnB;YACE,OAAO;YACP,UAAU;YACV,aAAa;YACb,YAAY;YACZ,MAAM;YACN,QAAQ;QACV;QACA;YACE,OAAO;YACP,UAAU;YACV,aAAa;YACb,YAAY;YACZ,MAAM;YACN,QAAQ;QACV;QACA;YACE,OAAO;YACP,UAAU;YACV,aAAa;YACb,YAAY;YACZ,MAAM;YACN,QAAQ;QACV;KACD;IAED,qBACE,8OAAC;QAAI,WAAW,0IAAA,CAAA,UAAM,CAAC,YAAY;;0BAEjC,8OAAC;gBAAQ,WAAW,0IAAA,CAAA,UAAM,CAAC,IAAI;0BAC7B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAW,0IAAA,CAAA,UAAM,CAAC,WAAW;;0CAChC,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;gCAAE,WAAW,0IAAA,CAAA,UAAM,CAAC,eAAe;0CAAE;;;;;;;;;;;;;;;;;;;;;;0BAS5C,8OAAC;gBAAQ,WAAW,0IAAA,CAAA,UAAM,CAAC,YAAY;0BACrC,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAW,0IAAA,CAAA,UAAM,CAAC,SAAS;kCAC7B,aAAa,GAAG,CAAC,CAAC,SAAS,sBAC1B,8OAAC,gIAAA,CAAA,UAAW;gCAEV,OAAO,QAAQ,KAAK;gCACpB,UAAU,QAAQ,QAAQ;gCAC1B,aAAa,QAAQ,WAAW;gCAChC,YAAY,QAAQ,UAAU;gCAC9B,MAAM,QAAQ,IAAI;gCAClB,QAAQ,QAAQ,MAAM;+BANjB;;;;;;;;;;;;;;;;;;;;0BAcf,8OAAC;gBAAQ,WAAW,0IAAA,CAAA,UAAM,CAAC,WAAW;0BACpC,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAW,0IAAA,CAAA,UAAM,CAAC,WAAW;;0CAChC,8OAAC;gCAAI,WAAW,0IAAA,CAAA,UAAM,CAAC,QAAQ;;kDAC7B,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;wCAAG,WAAW,0IAAA,CAAA,UAAM,CAAC,SAAS;;0DAC7B,8OAAC;;kEACC,8OAAC;kEAAO;;;;;;oDAA6B;;;;;;;0DAGvC,8OAAC;;kEACC,8OAAC;kEAAO;;;;;;oDAAwB;;;;;;;0DAGlC,8OAAC;;kEACC,8OAAC;kEAAO;;;;;;oDAAyB;;;;;;;0DAGnC,8OAAC;;kEACC,8OAAC;kEAAO;;;;;;oDAAsB;;;;;;;;;;;;;;;;;;;0CAKpC,8OAAC;gCAAI,WAAW,0IAAA,CAAA,UAAM,CAAC,WAAW;;kDAChC,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAE;;;;;;kDAIH,8OAAC;wCAAI,WAAW,0IAAA,CAAA,UAAM,CAAC,cAAc;;0DACnC,8OAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,8OAAC;gBAAQ,WAAW,0IAAA,CAAA,UAAM,CAAC,SAAS;0BAClC,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAW,0IAAA,CAAA,UAAM,CAAC,gBAAgB;;0CACrC,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;0CAIH,8OAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 549, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 587, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,GAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}