{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/CTAButtons.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"btn\": \"CTAButtons-module__AIDVgW__btn\",\n  \"ctaContainer\": \"CTAButtons-module__AIDVgW__ctaContainer\",\n  \"horizontal\": \"CTAButtons-module__AIDVgW__horizontal\",\n  \"small\": \"CTAButtons-module__AIDVgW__small\",\n  \"vertical\": \"CTAButtons-module__AIDVgW__vertical\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/src/components/CTAButtons.js"], "sourcesContent": ["import styles from './CTAButtons.module.css';\n\nexport default function CTAButtons({ \n  layout = 'horizontal', \n  size = 'default',\n  showBooking = true,\n  className = '' \n}) {\n  const containerClass = `${styles.ctaContainer} ${styles[layout]} ${styles[size]} ${className}`;\n\n  return (\n    <div className={containerClass}>\n      {showBooking && (\n        <a \n          href=\"/bookings\" \n          className=\"btn btn-primary btn-large\"\n          aria-label=\"Book an appointment\"\n        >\n          Book Appointment\n        </a>\n      )}\n      \n      <a \n        href=\"tel:+***********\" \n        className=\"btn btn-large\"\n        aria-label=\"Call Tuffside Automotive Garage\"\n      >\n        📞 Call Now\n      </a>\n      \n      <a \n        href=\"https://wa.me/***********\" \n        target=\"_blank\" \n        rel=\"noopener noreferrer\"\n        className=\"btn btn-large\"\n        aria-label=\"Message us on WhatsApp\"\n      >\n        💬 WhatsApp\n      </a>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS,WAAW,EACjC,SAAS,YAAY,EACrB,OAAO,SAAS,EAChB,cAAc,IAAI,EAClB,YAAY,EAAE,EACf;IACC,MAAM,iBAAiB,GAAG,2IAAA,CAAA,UAAM,CAAC,YAAY,CAAC,CAAC,EAAE,2IAAA,CAAA,UAAM,CAAC,OAAO,CAAC,CAAC,EAAE,2IAAA,CAAA,UAAM,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;IAE9F,qBACE,8OAAC;QAAI,WAAW;;YACb,6BACC,8OAAC;gBACC,MAAK;gBACL,WAAU;gBACV,cAAW;0BACZ;;;;;;0BAKH,8OAAC;gBACC,MAAK;gBACL,WAAU;gBACV,cAAW;0BACZ;;;;;;0BAID,8OAAC;gBACC,MAAK;gBACL,QAAO;gBACP,KAAI;gBACJ,WAAU;gBACV,cAAW;0BACZ;;;;;;;;;;;;AAKP", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/contact/page.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"areasList\": \"page-module__OSLHOG__areasList\",\n  \"businessInfo\": \"page-module__OSLHOG__businessInfo\",\n  \"contactDetails\": \"page-module__OSLHOG__contactDetails\",\n  \"contactGrid\": \"page-module__OSLHOG__contactGrid\",\n  \"contactIcon\": \"page-module__OSLHOG__contactIcon\",\n  \"contactInfo\": \"page-module__OSLHOG__contactInfo\",\n  \"contactItem\": \"page-module__OSLHOG__contactItem\",\n  \"contactLink\": \"page-module__OSLHOG__contactLink\",\n  \"contactNote\": \"page-module__OSLHOG__contactNote\",\n  \"contactPage\": \"page-module__OSLHOG__contactPage\",\n  \"contactText\": \"page-module__OSLHOG__contactText\",\n  \"cta\": \"page-module__OSLHOG__cta\",\n  \"ctaContent\": \"page-module__OSLHOG__ctaContent\",\n  \"emergency\": \"page-module__OSLHOG__emergency\",\n  \"emergencyContent\": \"page-module__OSLHOG__emergencyContent\",\n  \"emergencyNote\": \"page-module__OSLHOG__emergencyNote\",\n  \"hero\": \"page-module__OSLHOG__hero\",\n  \"heroContent\": \"page-module__OSLHOG__heroContent\",\n  \"heroDescription\": \"page-module__OSLHOG__heroDescription\",\n  \"hourItem\": \"page-module__OSLHOG__hourItem\",\n  \"hours\": \"page-module__OSLHOG__hours\",\n  \"hoursCard\": \"page-module__OSLHOG__hoursCard\",\n  \"mapContainer\": \"page-module__OSLHOG__mapContainer\",\n  \"mapPlaceholder\": \"page-module__OSLHOG__mapPlaceholder\",\n  \"mapSection\": \"page-module__OSLHOG__mapSection\",\n  \"serviceAreas\": \"page-module__OSLHOG__serviceAreas\",\n  \"servicesCard\": \"page-module__OSLHOG__servicesCard\",\n  \"servicesList\": \"page-module__OSLHOG__servicesList\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/src/app/contact/page.js"], "sourcesContent": ["import CTAButtons from '@/components/CTAButtons';\nimport styles from './page.module.css';\n\nexport const metadata = {\n  title: \"Contact Tuffside Automotive Garage - Get in Touch\",\n  description: \"Contact Tuffside Automotive Garage in Trinidad. Call ****** 335-7440, email <EMAIL>, or visit our location. Emergency services available 24/7.\",\n};\n\nexport default function Contact() {\n  return (\n    <div className={styles.contactPage}>\n      {/* Hero Section */}\n      <section className={styles.hero}>\n        <div className=\"container\">\n          <div className={styles.heroContent}>\n            <h1>Contact Us</h1>\n            <p className={styles.heroDescription}>\n              Get in touch with Tuffside Automotive Garage. We're here to help \n              with all your automotive needs, from routine maintenance to emergency repairs.\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* Contact Information */}\n      <section className={styles.contactInfo}>\n        <div className=\"container\">\n          <div className={styles.contactGrid}>\n            {/* Contact Details */}\n            <div className={styles.contactDetails}>\n              <h2>Get in Touch</h2>\n              \n              <div className={styles.contactItem}>\n                <div className={styles.contactIcon}>📞</div>\n                <div className={styles.contactText}>\n                  <h3>Phone</h3>\n                  <p>\n                    <a href=\"tel:+***********\" className={styles.contactLink}>\n                      ****** 335-7440\n                    </a>\n                  </p>\n                  <span className={styles.contactNote}>Available 24/7 for emergencies</span>\n                </div>\n              </div>\n\n              <div className={styles.contactItem}>\n                <div className={styles.contactIcon}>✉️</div>\n                <div className={styles.contactText}>\n                  <h3>Email</h3>\n                  <p>\n                    <a href=\"mailto:<EMAIL>\" className={styles.contactLink}>\n                      <EMAIL>\n                    </a>\n                  </p>\n                  <span className={styles.contactNote}>We'll respond within 24 hours</span>\n                </div>\n              </div>\n\n              <div className={styles.contactItem}>\n                <div className={styles.contactIcon}>💬</div>\n                <div className={styles.contactText}>\n                  <h3>WhatsApp</h3>\n                  <p>\n                    <a \n                      href=\"https://wa.me/***********\" \n                      target=\"_blank\" \n                      rel=\"noopener noreferrer\"\n                      className={styles.contactLink}\n                    >\n                      Message us on WhatsApp\n                    </a>\n                  </p>\n                  <span className={styles.contactNote}>Quick responses during business hours</span>\n                </div>\n              </div>\n\n              <div className={styles.contactItem}>\n                <div className={styles.contactIcon}>📍</div>\n                <div className={styles.contactText}>\n                  <h3>Location</h3>\n                  <p>Trinidad & Tobago</p>\n                  <span className={styles.contactNote}>Mobile service available island-wide</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Business Hours & Services */}\n            <div className={styles.businessInfo}>\n              <div className={styles.hoursCard}>\n                <h3>Business Hours</h3>\n                <div className={styles.hours}>\n                  <div className={styles.hourItem}>\n                    <span>Monday - Friday</span>\n                    <span>8:00 AM - 6:00 PM</span>\n                  </div>\n                  <div className={styles.hourItem}>\n                    <span>Saturday</span>\n                    <span>8:00 AM - 4:00 PM</span>\n                  </div>\n                  <div className={styles.hourItem}>\n                    <span>Sunday</span>\n                    <span>Emergency Only</span>\n                  </div>\n                </div>\n                <p className={styles.emergencyNote}>\n                  🚨 Emergency services available 24/7\n                </p>\n              </div>\n\n              <div className={styles.servicesCard}>\n                <h3>Quick Services</h3>\n                <ul className={styles.servicesList}>\n                  <li>Engine Diagnostics</li>\n                  <li>Diesel Repair</li>\n                  <li>Engine Tuning</li>\n                  <li>Suspension Work</li>\n                  <li>Electrical Systems</li>\n                  <li>General Maintenance</li>\n                  <li>Emergency Roadside</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Map Section */}\n      <section className={styles.mapSection}>\n        <div className=\"container\">\n          <h2 className=\"text-center mb-lg\">Our Service Area</h2>\n          <div className={styles.mapContainer}>\n            <div className={styles.mapPlaceholder}>\n              <h3>🗺️ Trinidad & Tobago</h3>\n              <p>Island-wide mobile service available</p>\n              <p>Emergency response across all regions</p>\n            </div>\n          </div>\n          <div className={styles.serviceAreas}>\n            <h4>We Service All Areas Including:</h4>\n            <div className={styles.areasList}>\n              <span>Port of Spain</span>\n              <span>San Fernando</span>\n              <span>Chaguanas</span>\n              <span>Arima</span>\n              <span>Point Fortin</span>\n              <span>Couva</span>\n              <span>Marabella</span>\n              <span>Sangre Grande</span>\n              <span>And surrounding areas</span>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Emergency Contact */}\n      <section className={styles.emergency}>\n        <div className=\"container\">\n          <div className={styles.emergencyContent}>\n            <h2>🚨 Emergency Breakdown?</h2>\n            <p>\n              Don't panic! We provide 24/7 emergency roadside assistance \n              across Trinidad & Tobago. Call us now for immediate help.\n            </p>\n            <a \n              href=\"tel:+***********\" \n              className=\"btn btn-primary btn-large\"\n            >\n              📞 Emergency Call: ****** 335-7440\n            </a>\n          </div>\n        </div>\n      </section>\n\n      {/* Call to Action */}\n      <section className={styles.cta}>\n        <div className=\"container\">\n          <div className={styles.ctaContent}>\n            <h2>Ready to Get Started?</h2>\n            <p>\n              Contact us today to schedule your service, get a quote, or ask any questions. \n              We're here to help keep you moving!\n            </p>\n            <CTAButtons layout=\"horizontal\" size=\"large\" />\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;AACf;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;;0BAEhC,8OAAC;gBAAQ,WAAW,yIAAA,CAAA,UAAM,CAAC,IAAI;0BAC7B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;;0CAChC,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;gCAAE,WAAW,yIAAA,CAAA,UAAM,CAAC,eAAe;0CAAE;;;;;;;;;;;;;;;;;;;;;;0BAS5C,8OAAC;gBAAQ,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;0BACpC,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;;0CAEhC,8OAAC;gCAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,cAAc;;kDACnC,8OAAC;kDAAG;;;;;;kDAEJ,8OAAC;wCAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;;0DAChC,8OAAC;gDAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;0DAAE;;;;;;0DACpC,8OAAC;gDAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;;kEAChC,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEACC,cAAA,8OAAC;4DAAE,MAAK;4DAAmB,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;sEAAE;;;;;;;;;;;kEAI5D,8OAAC;wDAAK,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;kEAAE;;;;;;;;;;;;;;;;;;kDAIzC,8OAAC;wCAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;;0DAChC,8OAAC;gDAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;0DAAE;;;;;;0DACpC,8OAAC;gDAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;;kEAChC,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEACC,cAAA,8OAAC;4DAAE,MAAK;4DAA2B,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;sEAAE;;;;;;;;;;;kEAIpE,8OAAC;wDAAK,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;kEAAE;;;;;;;;;;;;;;;;;;kDAIzC,8OAAC;wCAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;;0DAChC,8OAAC;gDAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;0DAAE;;;;;;0DACpC,8OAAC;gDAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;;kEAChC,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEACC,cAAA,8OAAC;4DACC,MAAK;4DACL,QAAO;4DACP,KAAI;4DACJ,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;sEAC9B;;;;;;;;;;;kEAIH,8OAAC;wDAAK,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;kEAAE;;;;;;;;;;;;;;;;;;kDAIzC,8OAAC;wCAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;;0DAChC,8OAAC;gDAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;0DAAE;;;;;;0DACpC,8OAAC;gDAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;;kEAChC,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAE;;;;;;kEACH,8OAAC;wDAAK,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;kEAAE;;;;;;;;;;;;;;;;;;;;;;;;0CAM3C,8OAAC;gCAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,YAAY;;kDACjC,8OAAC;wCAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;;0DAC9B,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;gDAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,KAAK;;kEAC1B,8OAAC;wDAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,QAAQ;;0EAC7B,8OAAC;0EAAK;;;;;;0EACN,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,QAAQ;;0EAC7B,8OAAC;0EAAK;;;;;;0EACN,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,QAAQ;;0EAC7B,8OAAC;0EAAK;;;;;;0EACN,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;0DAGV,8OAAC;gDAAE,WAAW,yIAAA,CAAA,UAAM,CAAC,aAAa;0DAAE;;;;;;;;;;;;kDAKtC,8OAAC;wCAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,YAAY;;0DACjC,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;gDAAG,WAAW,yIAAA,CAAA,UAAM,CAAC,YAAY;;kEAChC,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAShB,8OAAC;gBAAQ,WAAW,yIAAA,CAAA,UAAM,CAAC,UAAU;0BACnC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoB;;;;;;sCAClC,8OAAC;4BAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,YAAY;sCACjC,cAAA,8OAAC;gCAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,cAAc;;kDACnC,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAE;;;;;;kDACH,8OAAC;kDAAE;;;;;;;;;;;;;;;;;sCAGP,8OAAC;4BAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,YAAY;;8CACjC,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;oCAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;;sDAC9B,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOd,8OAAC;gBAAQ,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;0BAClC,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,gBAAgB;;0CACrC,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;0CAIH,8OAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAQ,WAAW,yIAAA,CAAA,UAAM,CAAC,GAAG;0BAC5B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,UAAU;;0CAC/B,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;0CAIH,8OAAC,+HAAA,CAAA,UAAU;gCAAC,QAAO;gCAAa,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjD", "debugId": null}}, {"offset": {"line": 907, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 945, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,IAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}