{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/CTAButtons.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"btn\": \"CTAButtons-module__AIDVgW__btn\",\n  \"ctaContainer\": \"CTAButtons-module__AIDVgW__ctaContainer\",\n  \"horizontal\": \"CTAButtons-module__AIDVgW__horizontal\",\n  \"small\": \"CTAButtons-module__AIDVgW__small\",\n  \"vertical\": \"CTAButtons-module__AIDVgW__vertical\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/src/components/CTAButtons.js"], "sourcesContent": ["import styles from './CTAButtons.module.css';\n\nexport default function CTAButtons({ \n  layout = 'horizontal', \n  size = 'default',\n  showBooking = true,\n  className = '' \n}) {\n  const containerClass = `${styles.ctaContainer} ${styles[layout]} ${styles[size]} ${className}`;\n\n  return (\n    <div className={containerClass}>\n      {showBooking && (\n        <a \n          href=\"/bookings\" \n          className=\"btn btn-primary btn-large\"\n          aria-label=\"Book an appointment\"\n        >\n          Book Appointment\n        </a>\n      )}\n      \n      <a \n        href=\"tel:+18683357440\" \n        className=\"btn btn-large\"\n        aria-label=\"Call Tuffside Automotive Garage\"\n      >\n        📞 Call Now\n      </a>\n      \n      <a \n        href=\"https://wa.me/18683357440\" \n        target=\"_blank\" \n        rel=\"noopener noreferrer\"\n        className=\"btn btn-large\"\n        aria-label=\"Message us on WhatsApp\"\n      >\n        💬 WhatsApp\n      </a>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS,WAAW,EACjC,SAAS,YAAY,EACrB,OAAO,SAAS,EAChB,cAAc,IAAI,EAClB,YAAY,EAAE,EACf;IACC,MAAM,iBAAiB,GAAG,2IAAA,CAAA,UAAM,CAAC,YAAY,CAAC,CAAC,EAAE,2IAAA,CAAA,UAAM,CAAC,OAAO,CAAC,CAAC,EAAE,2IAAA,CAAA,UAAM,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;IAE9F,qBACE,8OAAC;QAAI,WAAW;;YACb,6BACC,8OAAC;gBACC,MAAK;gBACL,WAAU;gBACV,cAAW;0BACZ;;;;;;0BAKH,8OAAC;gBACC,MAAK;gBACL,WAAU;gBACV,cAAW;0BACZ;;;;;;0BAID,8OAAC;gBACC,MAAK;gBACL,QAAO;gBACP,KAAI;gBACJ,WAAU;gBACV,cAAW;0BACZ;;;;;;;;;;;;AAKP", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/about/page.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"aboutPage\": \"page-module__NfDiEG__aboutPage\",\n  \"cta\": \"page-module__NfDiEG__cta\",\n  \"ctaContent\": \"page-module__NfDiEG__ctaContent\",\n  \"difference\": \"page-module__NfDiEG__difference\",\n  \"differenceContent\": \"page-module__NfDiEG__differenceContent\",\n  \"differenceList\": \"page-module__NfDiEG__differenceList\",\n  \"differenceStats\": \"page-module__NfDiEG__differenceStats\",\n  \"differenceText\": \"page-module__NfDiEG__differenceText\",\n  \"experienceBadge\": \"page-module__NfDiEG__experienceBadge\",\n  \"hero\": \"page-module__NfDiEG__hero\",\n  \"heroContent\": \"page-module__NfDiEG__heroContent\",\n  \"heroDescription\": \"page-module__NfDiEG__heroDescription\",\n  \"heroTagline\": \"page-module__NfDiEG__heroTagline\",\n  \"statCard\": \"page-module__NfDiEG__statCard\",\n  \"story\": \"page-module__NfDiEG__story\",\n  \"storyContent\": \"page-module__NfDiEG__storyContent\",\n  \"storyText\": \"page-module__NfDiEG__storyText\",\n  \"valueCard\": \"page-module__NfDiEG__valueCard\",\n  \"valueIcon\": \"page-module__NfDiEG__valueIcon\",\n  \"values\": \"page-module__NfDiEG__values\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/src/app/about/page.js"], "sourcesContent": ["import CTAButtons from '@/components/CTAButtons';\nimport styles from './page.module.css';\n\nexport const metadata = {\n  title: \"About Tuffside Automotive Garage - Our Story\",\n  description: \"Learn about Tuffside Automotive Garage's commitment to quality automotive repair and maintenance in Trinidad. Over 15 years of trusted service.\",\n};\n\nexport default function About() {\n  return (\n    <div className={styles.aboutPage}>\n      {/* Hero Section */}\n      <section className={styles.hero}>\n        <div className=\"container\">\n          <div className={styles.heroContent}>\n            <h1>About Tuffside</h1>\n            <p className={styles.heroTagline}>Built to Last. Tuned to Perform.</p>\n            <p className={styles.heroDescription}>\n              More than just a garage - we're your trusted automotive partners \n              committed to keeping you safely on the road.\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* Our Story */}\n      <section className={styles.story}>\n        <div className=\"container\">\n          <div className={styles.storyContent}>\n            <div className={styles.storyText}>\n              <h2>Our Story</h2>\n              <p>\n                Tuffside Automotive Garage was born from a simple belief: every vehicle \n                deserves honest, expert care, and every customer deserves to be treated \n                like family. What started as a small operation has grown into one of \n                Trinidad's most trusted automotive service providers.\n              </p>\n              <p>\n                For over 15 years, we've been the go-to garage for drivers who value \n                quality workmanship, fair pricing, and genuine customer service. We've \n                built our reputation one satisfied customer at a time, and we're proud \n                to serve our community with integrity and expertise.\n              </p>\n              <p>\n                Whether you drive a compact car, a heavy-duty truck, or anything in \n                between, we treat every vehicle with the same level of care and attention. \n                Because at Tuffside, we don't just fix cars - we keep families moving.\n              </p>\n            </div>\n            <div className={styles.storyImage}>\n              <div className={styles.experienceBadge}>\n                <h3>15+</h3>\n                <p>Years of Experience</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Our Values */}\n      <section className={styles.values}>\n        <div className=\"container\">\n          <h2 className=\"text-center mb-lg\">Our Values</h2>\n          <div className=\"grid grid-3\">\n            <div className={styles.valueCard}>\n              <div className={styles.valueIcon}>🤝</div>\n              <h3>Trust & Honesty</h3>\n              <p>\n                We believe in transparent communication and honest pricing. \n                No hidden fees, no unnecessary work - just straight talk about \n                what your vehicle needs.\n              </p>\n            </div>\n            <div className={styles.valueCard}>\n              <div className={styles.valueIcon}>⚡</div>\n              <h3>Quality & Speed</h3>\n              <p>\n                We know your time is valuable. That's why we work efficiently \n                without compromising on quality, getting you back on the road \n                as quickly as possible.\n              </p>\n            </div>\n            <div className={styles.valueCard}>\n              <div className={styles.valueIcon}>💪</div>\n              <h3>Expertise & Care</h3>\n              <p>\n                Our team brings years of experience and genuine passion for \n                automotive work. Every job gets our full attention, from routine \n                maintenance to complex repairs.\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* What Sets Us Apart */}\n      <section className={styles.difference}>\n        <div className=\"container\">\n          <div className={styles.differenceContent}>\n            <div className={styles.differenceText}>\n              <h2>What Sets Us Apart</h2>\n              <ul className={styles.differenceList}>\n                <li>\n                  <strong>Personal Service:</strong> We remember your name, your car, \n                  and your preferences. You're not just a number here.\n                </li>\n                <li>\n                  <strong>Fair Pricing:</strong> Competitive rates with no surprises. \n                  We'll always explain costs upfront.\n                </li>\n                <li>\n                  <strong>Emergency Response:</strong> Breakdowns don't wait for \n                  business hours. Neither do we.\n                </li>\n                <li>\n                  <strong>All Makes & Models:</strong> From vintage classics to \n                  modern hybrids, we work on everything.\n                </li>\n                <li>\n                  <strong>Guaranteed Work:</strong> We stand behind every job with \n                  our satisfaction guarantee.\n                </li>\n                <li>\n                  <strong>Community Focus:</strong> We're locals serving locals, \n                  invested in our community's success.\n                </li>\n              </ul>\n            </div>\n            <div className={styles.differenceStats}>\n              <div className={styles.statCard}>\n                <h3>500+</h3>\n                <p>Happy Customers</p>\n              </div>\n              <div className={styles.statCard}>\n                <h3>98%</h3>\n                <p>Customer Satisfaction</p>\n              </div>\n              <div className={styles.statCard}>\n                <h3>24/7</h3>\n                <p>Emergency Support</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Call to Action */}\n      <section className={styles.cta}>\n        <div className=\"container\">\n          <div className={styles.ctaContent}>\n            <h2>Ready to Experience the Tuffside Difference?</h2>\n            <p>\n              Join hundreds of satisfied customers who trust us with their vehicles. \n              Contact us today to schedule your service or get a quote.\n            </p>\n            <CTAButtons layout=\"horizontal\" size=\"large\" />\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;AACf;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;;0BAE9B,8OAAC;gBAAQ,WAAW,uIAAA,CAAA,UAAM,CAAC,IAAI;0BAC7B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,WAAW;;0CAChC,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;gCAAE,WAAW,uIAAA,CAAA,UAAM,CAAC,WAAW;0CAAE;;;;;;0CAClC,8OAAC;gCAAE,WAAW,uIAAA,CAAA,UAAM,CAAC,eAAe;0CAAE;;;;;;;;;;;;;;;;;;;;;;0BAS5C,8OAAC;gBAAQ,WAAW,uIAAA,CAAA,UAAM,CAAC,KAAK;0BAC9B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,YAAY;;0CACjC,8OAAC;gCAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;;kDAC9B,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAE;;;;;;kDAMH,8OAAC;kDAAE;;;;;;kDAMH,8OAAC;kDAAE;;;;;;;;;;;;0CAML,8OAAC;gCAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,UAAU;0CAC/B,cAAA,8OAAC;oCAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,eAAe;;sDACpC,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQb,8OAAC;gBAAQ,WAAW,uIAAA,CAAA,UAAM,CAAC,MAAM;0BAC/B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoB;;;;;;sCAClC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;;sDAC9B,8OAAC;4CAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;sDAAE;;;;;;sDAClC,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAE;;;;;;;;;;;;8CAML,8OAAC;oCAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;;sDAC9B,8OAAC;4CAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;sDAAE;;;;;;sDAClC,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAE;;;;;;;;;;;;8CAML,8OAAC;oCAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;;sDAC9B,8OAAC;4CAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;sDAAE;;;;;;sDAClC,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWX,8OAAC;gBAAQ,WAAW,uIAAA,CAAA,UAAM,CAAC,UAAU;0BACnC,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,iBAAiB;;0CACtC,8OAAC;gCAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,cAAc;;kDACnC,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;wCAAG,WAAW,uIAAA,CAAA,UAAM,CAAC,cAAc;;0DAClC,8OAAC;;kEACC,8OAAC;kEAAO;;;;;;oDAA0B;;;;;;;0DAGpC,8OAAC;;kEACC,8OAAC;kEAAO;;;;;;oDAAsB;;;;;;;0DAGhC,8OAAC;;kEACC,8OAAC;kEAAO;;;;;;oDAA4B;;;;;;;0DAGtC,8OAAC;;kEACC,8OAAC;kEAAO;;;;;;oDAA4B;;;;;;;0DAGtC,8OAAC;;kEACC,8OAAC;kEAAO;;;;;;oDAAyB;;;;;;;0DAGnC,8OAAC;;kEACC,8OAAC;kEAAO;;;;;;oDAAyB;;;;;;;;;;;;;;;;;;;0CAKvC,8OAAC;gCAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,eAAe;;kDACpC,8OAAC;wCAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,QAAQ;;0DAC7B,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAE;;;;;;;;;;;;kDAEL,8OAAC;wCAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,QAAQ;;0DAC7B,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAE;;;;;;;;;;;;kDAEL,8OAAC;wCAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,QAAQ;;0DAC7B,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQb,8OAAC;gBAAQ,WAAW,uIAAA,CAAA,UAAM,CAAC,GAAG;0BAC5B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,UAAU;;0CAC/B,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;0CAIH,8OAAC,+HAAA,CAAA,UAAU;gCAAC,QAAO;gCAAa,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjD", "debugId": null}}, {"offset": {"line": 708, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 746, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,MAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}