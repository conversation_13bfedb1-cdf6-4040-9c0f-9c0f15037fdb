{"version": 3, "sources": [], "sections": [{"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Header.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"ctaButtons\": \"Header-module__hBw1pG__ctaButtons\",\n  \"fadeInDown\": \"Header-module__hBw1pG__fadeInDown\",\n  \"hamburger\": \"Header-module__hBw1pG__hamburger\",\n  \"header\": \"Header-module__hBw1pG__header\",\n  \"headerContent\": \"Header-module__hBw1pG__headerContent\",\n  \"logo\": \"Header-module__hBw1pG__logo\",\n  \"mobileCta\": \"Header-module__hBw1pG__mobileCta\",\n  \"mobileMenuButton\": \"Header-module__hBw1pG__mobileMenuButton\",\n  \"mobileNav\": \"Header-module__hBw1pG__mobileNav\",\n  \"mobileNavLink\": \"Header-module__hBw1pG__mobileNavLink\",\n  \"mobileNavOpen\": \"Header-module__hBw1pG__mobileNavOpen\",\n  \"moreButton\": \"Header-module__hBw1pG__moreButton\",\n  \"moreDropdown\": \"Header-module__hBw1pG__moreDropdown\",\n  \"moreIcon\": \"Header-module__hBw1pG__moreIcon\",\n  \"moreIconOpen\": \"Header-module__hBw1pG__moreIconOpen\",\n  \"moreMenu\": \"Header-module__hBw1pG__moreMenu\",\n  \"moreMenuItem\": \"Header-module__hBw1pG__moreMenuItem\",\n  \"nav\": \"Header-module__hBw1pG__nav\",\n  \"navLink\": \"Header-module__hBw1pG__navLink\",\n  \"tagline\": \"Header-module__hBw1pG__tagline\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/src/components/Header.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport styles from './Header.module.css';\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isMoreMenuOpen, setIsMoreMenuOpen] = useState(false);\n  const [visibleItems, setVisibleItems] = useState(5); // Start with all items visible\n  const [isMobile, setIsMobile] = useState(false);\n  const pathname = usePathname();\n\n  const navRef = useRef(null);\n  const moreButtonRef = useRef(null);\n  const moreMenuRef = useRef(null);\n\n  // Define all navigation items\n  const navItems = [\n    { href: '/about', label: 'About', show: pathname !== '/about' },\n    { href: '/services', label: 'Services', show: pathname !== '/services' },\n    { href: '/bookings', label: 'Bookings', show: pathname !== '/bookings' },\n    { href: '/testimonials', label: 'Testimonials', show: pathname !== '/testimonials' },\n    { href: '/contact', label: 'Contact', show: pathname !== '/contact' }\n  ].filter(item => item.show);\n\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n\n  const toggleMoreMenu = () => {\n    setIsMoreMenuOpen(!isMoreMenuOpen);\n  };\n\n  const closeMoreMenu = () => {\n    setIsMoreMenuOpen(false);\n  };\n\n  // Handle window resize and mobile detection\n  useEffect(() => {\n    const handleResize = () => {\n      const windowWidth = window.innerWidth;\n      const mobile = windowWidth <= 768;\n      setIsMobile(mobile);\n\n      if (!mobile) {\n        // Simple breakpoint-based approach for greedy navigation\n        let newVisibleItems = navItems.length;\n\n        if (windowWidth <= 1200 && windowWidth > 1000) {\n          newVisibleItems = Math.max(3, navItems.length - 2); // Hide last 2 items\n        } else if (windowWidth <= 1000 && windowWidth > 900) {\n          newVisibleItems = Math.max(2, navItems.length - 3); // Hide last 3 items\n        } else if (windowWidth <= 900 && windowWidth > 768) {\n          newVisibleItems = Math.max(1, navItems.length - 4); // Hide last 4 items\n        }\n\n        // Don't show \"More\" for just one item\n        if (navItems.length - newVisibleItems === 1) {\n          newVisibleItems = navItems.length;\n        }\n\n        setVisibleItems(newVisibleItems);\n      } else {\n        setVisibleItems(navItems.length);\n        setIsMoreMenuOpen(false);\n      }\n    };\n\n    // Initial calculation\n    handleResize();\n\n    // Window resize listener\n    window.addEventListener('resize', handleResize);\n\n    return () => {\n      window.removeEventListener('resize', handleResize);\n    };\n  }, [navItems, isMobile]);\n\n  // Handle click outside to close more menu\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (\n        moreMenuRef.current &&\n        !moreMenuRef.current.contains(event.target) &&\n        moreButtonRef.current &&\n        !moreButtonRef.current.contains(event.target)\n      ) {\n        setIsMoreMenuOpen(false);\n      }\n    };\n\n    if (isMoreMenuOpen) {\n      document.addEventListener('mousedown', handleClickOutside);\n    }\n\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, [isMoreMenuOpen]);\n\n  // Handle keyboard navigation\n  const handleMoreMenuKeyDown = (event) => {\n    if (event.key === 'Escape') {\n      setIsMoreMenuOpen(false);\n      moreButtonRef.current?.focus();\n    }\n  };\n\n  const handleMoreButtonKeyDown = (event) => {\n    if (event.key === 'Enter' || event.key === ' ') {\n      event.preventDefault();\n      toggleMoreMenu();\n    }\n  };\n\n  return (\n    <header className={styles.header}>\n      <div className=\"container\">\n        <div className={styles.headerContent}>\n          {/* Logo */}\n          <Link href=\"/\" className={styles.logo}>\n            <h1>Tuffside</h1>\n            <span className={styles.tagline}>Automotive Garage</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className={styles.nav} ref={navRef}>\n            {/* Visible navigation items */}\n            {navItems.slice(0, visibleItems).map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className={styles.navLink}\n              >\n                {item.label}\n              </Link>\n            ))}\n\n            {/* More dropdown button - only show if there are hidden items and not mobile */}\n            {!isMobile && visibleItems < navItems.length && (\n              <div className={styles.moreDropdown}>\n                <button\n                  ref={moreButtonRef}\n                  className={styles.moreButton}\n                  onClick={toggleMoreMenu}\n                  onKeyDown={handleMoreButtonKeyDown}\n                  aria-expanded={isMoreMenuOpen}\n                  aria-haspopup=\"true\"\n                  aria-label=\"More navigation options\"\n                >\n                  <span>More</span>\n                  <svg\n                    className={`${styles.moreIcon} ${isMoreMenuOpen ? styles.moreIconOpen : ''}`}\n                    width=\"16\"\n                    height=\"16\"\n                    viewBox=\"0 0 16 16\"\n                    fill=\"currentColor\"\n                  >\n                    <path d=\"M8 12l-4-4h8l-4 4z\"/>\n                  </svg>\n                </button>\n\n                {/* Dropdown menu */}\n                {isMoreMenuOpen && (\n                  <div\n                    ref={moreMenuRef}\n                    className={styles.moreMenu}\n                    onKeyDown={handleMoreMenuKeyDown}\n                    role=\"menu\"\n                    aria-label=\"Additional navigation options\"\n                  >\n                    {navItems.slice(visibleItems).map((item) => (\n                      <Link\n                        key={item.href}\n                        href={item.href}\n                        className={styles.moreMenuItem}\n                        onClick={closeMoreMenu}\n                        role=\"menuitem\"\n                      >\n                        {item.label}\n                      </Link>\n                    ))}\n                  </div>\n                )}\n              </div>\n            )}\n          </nav>\n\n          {/* CTA Buttons */}\n          <div className={styles.ctaButtons}>\n            <a\n              href=\"tel:+18683357440\"\n              className=\"btn btn-primary\"\n              aria-label=\"Call Tuffside Automotive Garage\"\n            >\n              Call Now\n            </a>\n            <a\n              href=\"https://wa.me/18683357440\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"btn\"\n              aria-label=\"Message us on WhatsApp\"\n            >\n              WhatsApp\n            </a>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            className={styles.mobileMenuButton}\n            onClick={toggleMenu}\n            aria-label=\"Toggle mobile menu\"\n            aria-expanded={isMenuOpen}\n          >\n            <span className={styles.hamburger}></span>\n            <span className={styles.hamburger}></span>\n            <span className={styles.hamburger}></span>\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        <nav className={`${styles.mobileNav} ${isMenuOpen ? styles.mobileNavOpen : ''}`}>\n          {pathname !== '/' && (\n            <Link href=\"/\" className={styles.mobileNavLink} onClick={toggleMenu}>Home</Link>\n          )}\n          {pathname !== '/about' && <Link href=\"/about\" className={styles.mobileNavLink} onClick={toggleMenu}>About</Link>}\n          {pathname !== '/services' && <Link href=\"/services\" className={styles.mobileNavLink} onClick={toggleMenu}>Services</Link>}\n          {pathname !== '/bookings' && <Link href=\"/bookings\" className={styles.mobileNavLink} onClick={toggleMenu}>Bookings</Link>}\n          {pathname !== '/testimonials' && <Link href=\"/testimonials\" className={styles.mobileNavLink} onClick={toggleMenu}>Testimonials</Link>}\n          {pathname !== '/contact' && <Link href=\"/contact\" className={styles.mobileNavLink} onClick={toggleMenu}>Contact</Link>}\n\n          <div className={styles.mobileCta}>\n            <a\n              href=\"tel:+18683357440\"\n              className=\"btn btn-primary btn-large\"\n              onClick={toggleMenu}\n            >\n              Call Now\n            </a>\n            <a\n              href=\"https://wa.me/18683357440\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"btn btn-large\"\n              onClick={toggleMenu}\n            >\n              WhatsApp\n            </a>\n          </div>\n        </nav>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,+BAA+B;IACpF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACtB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC7B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,8BAA8B;IAC9B,MAAM,WAAW;QACf;YAAE,MAAM;YAAU,OAAO;YAAS,MAAM,aAAa;QAAS;QAC9D;YAAE,MAAM;YAAa,OAAO;YAAY,MAAM,aAAa;QAAY;QACvE;YAAE,MAAM;YAAa,OAAO;YAAY,MAAM,aAAa;QAAY;QACvE;YAAE,MAAM;YAAiB,OAAO;YAAgB,MAAM,aAAa;QAAgB;QACnF;YAAE,MAAM;YAAY,OAAO;YAAW,MAAM,aAAa;QAAW;KACrE,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI;IAE1B,MAAM,aAAa;QACjB,cAAc,CAAC;IACjB;IAEA,MAAM,iBAAiB;QACrB,kBAAkB,CAAC;IACrB;IAEA,MAAM,gBAAgB;QACpB,kBAAkB;IACpB;IAEA,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,MAAM,cAAc,OAAO,UAAU;YACrC,MAAM,SAAS,eAAe;YAC9B,YAAY;YAEZ,IAAI,CAAC,QAAQ;gBACX,yDAAyD;gBACzD,IAAI,kBAAkB,SAAS,MAAM;gBAErC,IAAI,eAAe,QAAQ,cAAc,MAAM;oBAC7C,kBAAkB,KAAK,GAAG,CAAC,GAAG,SAAS,MAAM,GAAG,IAAI,oBAAoB;gBAC1E,OAAO,IAAI,eAAe,QAAQ,cAAc,KAAK;oBACnD,kBAAkB,KAAK,GAAG,CAAC,GAAG,SAAS,MAAM,GAAG,IAAI,oBAAoB;gBAC1E,OAAO,IAAI,eAAe,OAAO,cAAc,KAAK;oBAClD,kBAAkB,KAAK,GAAG,CAAC,GAAG,SAAS,MAAM,GAAG,IAAI,oBAAoB;gBAC1E;gBAEA,sCAAsC;gBACtC,IAAI,SAAS,MAAM,GAAG,oBAAoB,GAAG;oBAC3C,kBAAkB,SAAS,MAAM;gBACnC;gBAEA,gBAAgB;YAClB,OAAO;gBACL,gBAAgB,SAAS,MAAM;gBAC/B,kBAAkB;YACpB;QACF;QAEA,sBAAsB;QACtB;QAEA,yBAAyB;QACzB,OAAO,gBAAgB,CAAC,UAAU;QAElC,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;QACvC;IACF,GAAG;QAAC;QAAU;KAAS;IAEvB,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IACE,YAAY,OAAO,IACnB,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,KAC1C,cAAc,OAAO,IACrB,CAAC,cAAc,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAC5C;gBACA,kBAAkB;YACpB;QACF;QAEA,IAAI,gBAAgB;YAClB,SAAS,gBAAgB,CAAC,aAAa;QACzC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG;QAAC;KAAe;IAEnB,6BAA6B;IAC7B,MAAM,wBAAwB,CAAC;QAC7B,IAAI,MAAM,GAAG,KAAK,UAAU;YAC1B,kBAAkB;YAClB,cAAc,OAAO,EAAE;QACzB;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,IAAI,MAAM,GAAG,KAAK,WAAW,MAAM,GAAG,KAAK,KAAK;YAC9C,MAAM,cAAc;YACpB;QACF;IACF;IAEA,qBACE,8OAAC;QAAO,WAAW,uIAAA,CAAA,UAAM,CAAC,MAAM;kBAC9B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,aAAa;;sCAElC,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,IAAI;;8CACnC,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;oCAAK,WAAW,uIAAA,CAAA,UAAM,CAAC,OAAO;8CAAE;;;;;;;;;;;;sCAInC,8OAAC;4BAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,GAAG;4BAAE,KAAK;;gCAE9B,SAAS,KAAK,CAAC,GAAG,cAAc,GAAG,CAAC,CAAC,qBACpC,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,uIAAA,CAAA,UAAM,CAAC,OAAO;kDAExB,KAAK,KAAK;uCAJN,KAAK,IAAI;;;;;gCASjB,CAAC,YAAY,eAAe,SAAS,MAAM,kBAC1C,8OAAC;oCAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,YAAY;;sDACjC,8OAAC;4CACC,KAAK;4CACL,WAAW,uIAAA,CAAA,UAAM,CAAC,UAAU;4CAC5B,SAAS;4CACT,WAAW;4CACX,iBAAe;4CACf,iBAAc;4CACd,cAAW;;8DAEX,8OAAC;8DAAK;;;;;;8DACN,8OAAC;oDACC,WAAW,GAAG,uIAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,iBAAiB,uIAAA,CAAA,UAAM,CAAC,YAAY,GAAG,IAAI;oDAC5E,OAAM;oDACN,QAAO;oDACP,SAAQ;oDACR,MAAK;8DAEL,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;wCAKX,gCACC,8OAAC;4CACC,KAAK;4CACL,WAAW,uIAAA,CAAA,UAAM,CAAC,QAAQ;4CAC1B,WAAW;4CACX,MAAK;4CACL,cAAW;sDAEV,SAAS,KAAK,CAAC,cAAc,GAAG,CAAC,CAAC,qBACjC,8OAAC,4JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,WAAW,uIAAA,CAAA,UAAM,CAAC,YAAY;oDAC9B,SAAS;oDACT,MAAK;8DAEJ,KAAK,KAAK;mDANN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;sCAgB5B,8OAAC;4BAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,UAAU;;8CAC/B,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,cAAW;8CACZ;;;;;;8CAGD,8OAAC;oCACC,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;oCACV,cAAW;8CACZ;;;;;;;;;;;;sCAMH,8OAAC;4BACC,WAAW,uIAAA,CAAA,UAAM,CAAC,gBAAgB;4BAClC,SAAS;4BACT,cAAW;4BACX,iBAAe;;8CAEf,8OAAC;oCAAK,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;;;;;;8CACjC,8OAAC;oCAAK,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;;;;;;8CACjC,8OAAC;oCAAK,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;;;;;;;;;;;;;;;;;;8BAKrC,8OAAC;oBAAI,WAAW,GAAG,uIAAA,CAAA,UAAM,CAAC,SAAS,CAAC,CAAC,EAAE,aAAa,uIAAA,CAAA,UAAM,CAAC,aAAa,GAAG,IAAI;;wBAC5E,aAAa,qBACZ,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,aAAa;4BAAE,SAAS;sCAAY;;;;;;wBAEtE,aAAa,0BAAY,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAS,WAAW,uIAAA,CAAA,UAAM,CAAC,aAAa;4BAAE,SAAS;sCAAY;;;;;;wBACnG,aAAa,6BAAe,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAY,WAAW,uIAAA,CAAA,UAAM,CAAC,aAAa;4BAAE,SAAS;sCAAY;;;;;;wBACzG,aAAa,6BAAe,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAY,WAAW,uIAAA,CAAA,UAAM,CAAC,aAAa;4BAAE,SAAS;sCAAY;;;;;;wBACzG,aAAa,iCAAmB,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAgB,WAAW,uIAAA,CAAA,UAAM,CAAC,aAAa;4BAAE,SAAS;sCAAY;;;;;;wBACjH,aAAa,4BAAc,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAW,WAAW,uIAAA,CAAA,UAAM,CAAC,aAAa;4BAAE,SAAS;sCAAY;;;;;;sCAExG,8OAAC;4BAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;;8CAC9B,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS;8CACV;;;;;;8CAGD,8OAAC;oCACC,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;oCACV,SAAS;8CACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}