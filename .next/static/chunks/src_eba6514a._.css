/* [project]/src/components/CTAButtons.module.css [app-client] (css) */
.CTAButtons-module__AIDVgW__ctaContainer {
  gap: var(--spacing-md);
  justify-content: center;
  align-items: center;
  display: flex;
}

.CTAButtons-module__AIDVgW__horizontal {
  flex-flow: wrap;
}

.CTAButtons-module__AIDVgW__vertical {
  flex-direction: column;
  align-items: stretch;
}

.CTAButtons-module__AIDVgW__small .CTAButtons-module__AIDVgW__btn {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: .875rem;
}

@media (width <= 768px) {
  .CTAButtons-module__AIDVgW__horizontal {
    flex-direction: column;
    align-items: stretch;
  }

  .CTAButtons-module__AIDVgW__ctaContainer {
    gap: var(--spacing-sm);
  }
}

@media (width <= 480px) {
  .CTAButtons-module__AIDVgW__ctaContainer .CTAButtons-module__AIDVgW__btn {
    text-align: center;
    width: 100%;
  }
}


/* [project]/src/app/services/page.module.css [app-client] (css) */
.page-module__VKYseq__servicesPage {
  min-height: 100vh;
}

.page-module__VKYseq__hero {
  padding: var(--spacing-2xl) 0;
  background: linear-gradient(135deg, var(--color-black) 0%, #1a1a1a 100%);
  text-align: center;
}

.page-module__VKYseq__heroContent h1 {
  color: var(--color-white);
  margin-bottom: var(--spacing-md);
  font-size: 4rem;
}

.page-module__VKYseq__heroDescription {
  color: var(--color-grey);
  max-width: 800px;
  margin: 0 auto;
  font-size: 1.25rem;
  line-height: 1.6;
}

.page-module__VKYseq__servicesGrid {
  padding: var(--spacing-2xl) 0;
  background-color: #111;
}

.page-module__VKYseq__serviceCard {
  background-color: var(--color-black);
  border: 2px solid var(--color-grey);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  flex-direction: column;
  height: 100%;
  transition: all .3s;
  display: flex;
}

.page-module__VKYseq__serviceCard:hover {
  border-color: var(--color-red);
  box-shadow: var(--shadow-lg);
  transform: translateY(-5px);
}

.page-module__VKYseq__serviceHeader {
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  display: flex;
}

.page-module__VKYseq__serviceIcon {
  flex-shrink: 0;
  font-size: 2.5rem;
}

.page-module__VKYseq__serviceCard h3 {
  color: var(--color-white);
  margin: 0;
  font-size: 1.5rem;
}

.page-module__VKYseq__serviceDescription {
  color: var(--color-grey);
  margin-bottom: var(--spacing-md);
  flex-grow: 1;
  line-height: 1.6;
}

.page-module__VKYseq__serviceFeatures {
  margin: 0;
  padding: 0;
  list-style: none;
}

.page-module__VKYseq__serviceFeatures li {
  color: var(--color-grey);
  padding: var(--spacing-xs) 0;
  border-bottom: 1px solid #ffffff1a;
  transition: color .3s;
}

.page-module__VKYseq__serviceFeatures li:hover {
  color: var(--color-white);
}

.page-module__VKYseq__serviceFeatures li:last-child {
  border-bottom: none;
}

.page-module__VKYseq__whyChoose {
  padding: var(--spacing-2xl) 0;
  background-color: var(--color-black);
}

.page-module__VKYseq__benefitCard {
  border: 1px solid var(--color-grey);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  text-align: center;
  background-color: #111;
  transition: all .3s;
}

.page-module__VKYseq__benefitCard:hover {
  border-color: var(--color-red);
  background-color: #dc26260d;
}

.page-module__VKYseq__benefitCard h4 {
  font-family: var(--font-sans);
  color: var(--color-red);
  margin-bottom: var(--spacing-sm);
  font-size: 1.25rem;
}

.page-module__VKYseq__benefitCard p {
  color: var(--color-grey);
  margin: 0;
  line-height: 1.6;
}

.page-module__VKYseq__cta {
  padding: var(--spacing-2xl) 0;
  background: linear-gradient(135deg, var(--color-red), #b91c1c);
  text-align: center;
}

.page-module__VKYseq__ctaContent h2 {
  color: var(--color-white);
  margin-bottom: var(--spacing-md);
  font-size: 2.5rem;
}

.page-module__VKYseq__ctaContent p {
  color: var(--color-white);
  margin-bottom: var(--spacing-xl);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  font-size: 1.25rem;
  line-height: 1.6;
}

@media (width <= 768px) {
  .page-module__VKYseq__heroContent h1 {
    font-size: 3rem;
  }

  .page-module__VKYseq__heroDescription {
    font-size: 1.125rem;
  }

  .page-module__VKYseq__serviceHeader {
    text-align: center;
    gap: var(--spacing-sm);
    flex-direction: column;
  }

  .page-module__VKYseq__serviceIcon {
    font-size: 3rem;
  }

  .page-module__VKYseq__serviceCard {
    padding: var(--spacing-lg);
  }

  .page-module__VKYseq__ctaContent h2 {
    font-size: 2rem;
  }
}

@media (width <= 480px) {
  .page-module__VKYseq__heroContent h1 {
    font-size: 2.5rem;
  }

  .page-module__VKYseq__serviceCard {
    padding: var(--spacing-md);
  }

  .page-module__VKYseq__serviceHeader {
    gap: var(--spacing-xs);
  }

  .page-module__VKYseq__serviceIcon {
    font-size: 2.5rem;
  }

  .page-module__VKYseq__benefitCard {
    padding: var(--spacing-md);
  }
}


/*# sourceMappingURL=src_eba6514a._.css.map*/