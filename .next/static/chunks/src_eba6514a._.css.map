{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/src/components/CTAButtons.module.css"], "sourcesContent": [".ctaContainer {\n  display: flex;\n  gap: var(--spacing-md);\n  align-items: center;\n  justify-content: center;\n}\n\n.horizontal {\n  flex-direction: row;\n  flex-wrap: wrap;\n}\n\n.vertical {\n  flex-direction: column;\n  align-items: stretch;\n}\n\n.default {\n  /* Default size - uses global btn styles */\n}\n\n.large {\n  /* Large size - uses btn-large class */\n}\n\n.small .btn {\n  padding: var(--spacing-xs) var(--spacing-sm);\n  font-size: 0.875rem;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .horizontal {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .ctaContainer {\n    gap: var(--spacing-sm);\n  }\n}\n\n@media (max-width: 480px) {\n  .ctaContainer .btn {\n    width: 100%;\n    text-align: center;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;AAOA;;;;AAKA;;;;;AAaA;;;;;AAMA;EACE;;;;;EAKA;;;;;AAKF;EACE", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/src/app/services/page.module.css"], "sourcesContent": [".servicesPage {\n  min-height: 100vh;\n}\n\n/* Hero Section */\n.hero {\n  padding: var(--spacing-2xl) 0;\n  background: linear-gradient(135deg, var(--color-black) 0%, #1a1a1a 100%);\n  text-align: center;\n}\n\n.heroContent h1 {\n  font-size: 4rem;\n  color: var(--color-white);\n  margin-bottom: var(--spacing-md);\n}\n\n.heroDescription {\n  font-size: 1.25rem;\n  color: var(--color-grey);\n  max-width: 800px;\n  margin: 0 auto;\n  line-height: 1.6;\n}\n\n/* Services Grid */\n.servicesGrid {\n  padding: var(--spacing-2xl) 0;\n  background-color: #111111;\n}\n\n.serviceCard {\n  background-color: var(--color-black);\n  border: 2px solid var(--color-grey);\n  border-radius: var(--radius-lg);\n  padding: var(--spacing-xl);\n  transition: all 0.3s ease;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.serviceCard:hover {\n  border-color: var(--color-red);\n  transform: translateY(-5px);\n  box-shadow: var(--shadow-lg);\n}\n\n.serviceHeader {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-md);\n  margin-bottom: var(--spacing-md);\n}\n\n.serviceIcon {\n  font-size: 2.5rem;\n  flex-shrink: 0;\n}\n\n.serviceCard h3 {\n  color: var(--color-white);\n  margin: 0;\n  font-size: 1.5rem;\n}\n\n.serviceDescription {\n  color: var(--color-grey);\n  line-height: 1.6;\n  margin-bottom: var(--spacing-md);\n  flex-grow: 1;\n}\n\n.serviceFeatures {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.serviceFeatures li {\n  color: var(--color-grey);\n  padding: var(--spacing-xs) 0;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  transition: color 0.3s ease;\n}\n\n.serviceFeatures li:hover {\n  color: var(--color-white);\n}\n\n.serviceFeatures li:last-child {\n  border-bottom: none;\n}\n\n/* Why Choose Section */\n.whyChoose {\n  padding: var(--spacing-2xl) 0;\n  background-color: var(--color-black);\n}\n\n.benefitCard {\n  background-color: #111111;\n  border: 1px solid var(--color-grey);\n  border-radius: var(--radius-md);\n  padding: var(--spacing-lg);\n  text-align: center;\n  transition: all 0.3s ease;\n}\n\n.benefitCard:hover {\n  border-color: var(--color-red);\n  background-color: rgba(220, 38, 38, 0.05);\n}\n\n.benefitCard h4 {\n  font-family: var(--font-sans);\n  color: var(--color-red);\n  margin-bottom: var(--spacing-sm);\n  font-size: 1.25rem;\n}\n\n.benefitCard p {\n  color: var(--color-grey);\n  line-height: 1.6;\n  margin: 0;\n}\n\n/* CTA Section */\n.cta {\n  padding: var(--spacing-2xl) 0;\n  background: linear-gradient(135deg, var(--color-red), #b91c1c);\n  text-align: center;\n}\n\n.ctaContent h2 {\n  color: var(--color-white);\n  font-size: 2.5rem;\n  margin-bottom: var(--spacing-md);\n}\n\n.ctaContent p {\n  color: var(--color-white);\n  font-size: 1.25rem;\n  margin-bottom: var(--spacing-xl);\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .heroContent h1 {\n    font-size: 3rem;\n  }\n\n  .heroDescription {\n    font-size: 1.125rem;\n  }\n\n  .serviceHeader {\n    flex-direction: column;\n    text-align: center;\n    gap: var(--spacing-sm);\n  }\n\n  .serviceIcon {\n    font-size: 3rem;\n  }\n\n  .serviceCard {\n    padding: var(--spacing-lg);\n  }\n\n  .ctaContent h2 {\n    font-size: 2rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .heroContent h1 {\n    font-size: 2.5rem;\n  }\n\n  .serviceCard {\n    padding: var(--spacing-md);\n  }\n\n  .serviceHeader {\n    gap: var(--spacing-xs);\n  }\n\n  .serviceIcon {\n    font-size: 2.5rem;\n  }\n\n  .benefitCard {\n    padding: var(--spacing-md);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;;;;;AASA;;;;;AAKA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;;;;AAOA;;;;;AAKA;;;;;;AAMA;;;;;;;AAOA;;;;;;AAMA;;;;;;;AAOA;;;;AAIA;;;;AAKA;;;;;AAKA;;;;;;;;;AASA;;;;;AAKA;;;;;;;AAOA;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;AAWA;EACE;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA", "debugId": null}}]}