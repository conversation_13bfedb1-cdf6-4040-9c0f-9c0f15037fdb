/* [project]/src/components/CTAButtons.module.css [app-client] (css) */
.CTAButtons-module__AIDVgW__ctaContainer {
  gap: var(--spacing-md);
  justify-content: center;
  align-items: center;
  display: flex;
}

.CTAButtons-module__AIDVgW__horizontal {
  flex-flow: wrap;
}

.CTAButtons-module__AIDVgW__vertical {
  flex-direction: column;
  align-items: stretch;
}

.CTAButtons-module__AIDVgW__small .CTAButtons-module__AIDVgW__btn {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: .875rem;
}

@media (width <= 768px) {
  .CTAButtons-module__AIDVgW__horizontal {
    flex-direction: column;
    align-items: stretch;
  }

  .CTAButtons-module__AIDVgW__ctaContainer {
    gap: var(--spacing-sm);
  }
}

@media (width <= 480px) {
  .CTAButtons-module__AIDVgW__ctaContainer .CTAButtons-module__AIDVgW__btn {
    text-align: center;
    width: 100%;
  }
}

/*# sourceMappingURL=src_components_CTAButtons_module_css_f9ee138c._.single.css.map*/