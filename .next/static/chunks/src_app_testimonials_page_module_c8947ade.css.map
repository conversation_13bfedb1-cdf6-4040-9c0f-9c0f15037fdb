{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/src/app/testimonials/page.module.css"], "sourcesContent": [".testimonialsPage {\n  min-height: 100vh;\n}\n\n/* Hero Section */\n.hero {\n  padding: var(--spacing-2xl) 0;\n  background: linear-gradient(135deg, var(--color-black) 0%, #1a1a1a 100%);\n  text-align: center;\n}\n\n.heroContent h1 {\n  font-size: 4rem;\n  color: var(--color-white);\n  margin-bottom: var(--spacing-md);\n}\n\n.heroDescription {\n  font-size: 1.25rem;\n  color: var(--color-grey);\n  max-width: 700px;\n  margin: 0 auto;\n  line-height: 1.6;\n}\n\n/* Stats Section */\n.stats {\n  padding: var(--spacing-xl) 0;\n  background-color: #111111;\n}\n\n.statsGrid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: var(--spacing-lg);\n}\n\n.statCard {\n  background-color: var(--color-black);\n  border: 2px solid var(--color-red);\n  border-radius: var(--radius-lg);\n  padding: var(--spacing-lg);\n  text-align: center;\n  transition: all 0.3s ease;\n}\n\n.statCard:hover {\n  transform: translateY(-5px);\n  box-shadow: var(--shadow-lg);\n}\n\n.statCard h3 {\n  font-size: 2.5rem;\n  color: var(--color-red);\n  margin-bottom: var(--spacing-xs);\n  font-weight: bold;\n}\n\n.statCard p {\n  color: var(--color-grey);\n  margin: 0;\n  text-transform: uppercase;\n  letter-spacing: 0.1em;\n  font-size: 0.875rem;\n}\n\n/* Testimonials Grid */\n.testimonials {\n  padding: var(--spacing-2xl) 0;\n  background-color: var(--color-black);\n}\n\n.testimonialsGrid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n  gap: var(--spacing-xl);\n}\n\n.testimonialCard {\n  background-color: #111111;\n  border: 2px solid var(--color-grey);\n  border-radius: var(--radius-lg);\n  padding: var(--spacing-xl);\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.testimonialCard::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background: linear-gradient(90deg, var(--color-red), #b91c1c);\n}\n\n.testimonialCard:hover {\n  border-color: var(--color-red);\n  transform: translateY(-5px);\n  box-shadow: var(--shadow-lg);\n}\n\n.cardHeader {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: var(--spacing-md);\n}\n\n.serviceTicket {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-xs);\n}\n\n.ticketId {\n  font-family: 'Courier New', monospace;\n  color: var(--color-red);\n  font-weight: bold;\n  font-size: 0.875rem;\n}\n\n.ticketDate {\n  color: var(--color-grey);\n  font-size: 0.75rem;\n  text-transform: uppercase;\n  letter-spacing: 0.1em;\n}\n\n.rating {\n  color: #ffd700;\n  font-size: 1.25rem;\n  line-height: 1;\n}\n\n.cardContent {\n  margin-bottom: var(--spacing-lg);\n}\n\n.review {\n  color: var(--color-grey);\n  font-size: 1.125rem;\n  line-height: 1.6;\n  font-style: italic;\n  margin: 0;\n  position: relative;\n}\n\n.review::before {\n  content: '\"';\n  font-size: 3rem;\n  color: var(--color-red);\n  position: absolute;\n  top: -10px;\n  left: -20px;\n  font-family: serif;\n  opacity: 0.5;\n}\n\n.cardFooter {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-end;\n  gap: var(--spacing-md);\n}\n\n.customerInfo h4 {\n  color: var(--color-white);\n  margin: 0 0 var(--spacing-xs) 0;\n  font-size: 1.125rem;\n}\n\n.vehicle {\n  color: var(--color-grey);\n  font-size: 0.875rem;\n  margin: 0 0 var(--spacing-xs) 0;\n}\n\n.location {\n  color: var(--color-red);\n  font-size: 0.75rem;\n  margin: 0;\n  text-transform: uppercase;\n  letter-spacing: 0.1em;\n}\n\n.serviceInfo {\n  text-align: right;\n}\n\n.serviceType {\n  background-color: var(--color-red);\n  color: var(--color-white);\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--radius-sm);\n  font-size: 0.75rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.1em;\n}\n\n/* CTA Section */\n.cta {\n  padding: var(--spacing-2xl) 0;\n  background: linear-gradient(135deg, var(--color-red), #b91c1c);\n  text-align: center;\n}\n\n.ctaContent h2 {\n  color: var(--color-white);\n  font-size: 2.5rem;\n  margin-bottom: var(--spacing-md);\n}\n\n.ctaContent p {\n  color: var(--color-white);\n  font-size: 1.25rem;\n  margin-bottom: var(--spacing-xl);\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n}\n\n.ctaButtons {\n  display: flex;\n  gap: var(--spacing-md);\n  justify-content: center;\n  align-items: center;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .heroContent h1 {\n    font-size: 3rem;\n  }\n\n  .heroDescription {\n    font-size: 1.125rem;\n  }\n\n  .testimonialsGrid {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-lg);\n  }\n\n  .testimonialCard {\n    padding: var(--spacing-lg);\n  }\n\n  .cardFooter {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: var(--spacing-sm);\n  }\n\n  .serviceInfo {\n    text-align: left;\n    align-self: stretch;\n  }\n\n  .ctaButtons {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .ctaContent h2 {\n    font-size: 2rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .heroContent h1 {\n    font-size: 2.5rem;\n  }\n\n  .statsGrid {\n    grid-template-columns: repeat(2, 1fr);\n    gap: var(--spacing-sm);\n  }\n\n  .statCard {\n    padding: var(--spacing-md);\n  }\n\n  .statCard h3 {\n    font-size: 2rem;\n  }\n\n  .testimonialCard {\n    padding: var(--spacing-md);\n  }\n\n  .review {\n    font-size: 1rem;\n  }\n\n  .review::before {\n    font-size: 2rem;\n    top: -5px;\n    left: -15px;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;;;;;AASA;;;;;AAKA;;;;;;AAMA;;;;;;;;;AASA;;;;;AAKA;;;;;;;AAOA;;;;;;;;AASA;;;;;AAKA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;;;AAMA;;;;;;;AAOA;;;;;;AAMA;;;;;;;AAOA;;;;;;;AAOA;;;;;;AAMA;;;;AAIA;;;;;;;;;AASA;;;;;;;;;;;AAWA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;;;AAQA;;;;AAIA;;;;;;;;;;;AAYA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;AAQA;EACE;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;;EAMA;;;;;EAKA;;;;;EAKA;;;;;AAKF;EACE;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA", "debugId": null}}]}