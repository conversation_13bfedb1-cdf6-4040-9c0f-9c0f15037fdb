{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/CTAButtons.module.css"], "sourcesContent": [".ctaContainer {\n  display: flex;\n  gap: var(--spacing-md);\n  align-items: center;\n  justify-content: center;\n}\n\n.horizontal {\n  flex-direction: row;\n  flex-wrap: wrap;\n}\n\n.vertical {\n  flex-direction: column;\n  align-items: stretch;\n}\n\n.default {\n  /* Default size - uses global btn styles */\n}\n\n.large {\n  /* Large size - uses btn-large class */\n}\n\n.small .btn {\n  padding: var(--spacing-xs) var(--spacing-sm);\n  font-size: 0.875rem;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .horizontal {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .ctaContainer {\n    gap: var(--spacing-sm);\n  }\n}\n\n@media (max-width: 480px) {\n  .ctaContainer .btn {\n    width: 100%;\n    text-align: center;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;AAOA;;;;AAKA;;;;;AAaA;;;;;AAMA;EACE;;;;;EAKA;;;;;AAKF;EACE"}}]}