/* [project]/src/components/BookingCard.module.css [app-client] (css) */
.BookingCard-module__CVpzLq__bookingCard {
  background-color: var(--color-black);
  border: 2px solid var(--color-grey);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  flex-direction: column;
  height: 100%;
  transition: all .3s;
  display: flex;
  position: relative;
  overflow: hidden;
}

.BookingCard-module__CVpzLq__bookingCard:hover {
  border-color: var(--color-red);
  box-shadow: var(--shadow-lg);
  transform: translateY(-5px);
}

.BookingCard-module__CVpzLq__urgent {
  border-color: var(--color-red);
  background: linear-gradient(135deg, var(--color-black), #1a0000);
}

.BookingCard-module__CVpzLq__urgent:hover {
  border-color: var(--color-white);
  box-shadow: 0 10px 25px #dc26264d;
}

.BookingCard-module__CVpzLq__cardHeader {
  align-items: flex-start;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  display: flex;
}

.BookingCard-module__CVpzLq__cardIcon {
  flex-shrink: 0;
  font-size: 3rem;
  line-height: 1;
}

.BookingCard-module__CVpzLq__cardTitle h3 {
  color: var(--color-white);
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 1.5rem;
  line-height: 1.2;
}

.BookingCard-module__CVpzLq__duration {
  color: var(--color-red);
  text-transform: uppercase;
  letter-spacing: .1em;
  font-size: .875rem;
  font-weight: 600;
}

.BookingCard-module__CVpzLq__cardContent {
  margin-bottom: var(--spacing-lg);
  flex-grow: 1;
}

.BookingCard-module__CVpzLq__description {
  color: var(--color-grey);
  margin: 0;
  font-size: 1rem;
  line-height: 1.6;
}

.BookingCard-module__CVpzLq__cardFooter {
  margin-top: auto;
}

.BookingCard-module__CVpzLq__bookingButton {
  text-align: center;
  text-transform: uppercase;
  letter-spacing: .05em;
  width: 100%;
  font-weight: 600;
}

.BookingCard-module__CVpzLq__urgentBadge {
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  background-color: var(--color-red);
  color: var(--color-white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  text-transform: uppercase;
  letter-spacing: .1em;
  font-size: .75rem;
  font-weight: 600;
  animation: 2s infinite BookingCard-module__CVpzLq__pulse;
  position: absolute;
}

@keyframes BookingCard-module__CVpzLq__pulse {
  0% {
    box-shadow: 0 0 #dc2626b3;
  }

  70% {
    box-shadow: 0 0 0 10px #dc262600;
  }

  100% {
    box-shadow: 0 0 #dc262600;
  }
}

@media (width <= 768px) {
  .BookingCard-module__CVpzLq__bookingCard {
    padding: var(--spacing-lg);
  }

  .BookingCard-module__CVpzLq__cardHeader {
    text-align: center;
    gap: var(--spacing-sm);
    flex-direction: column;
  }

  .BookingCard-module__CVpzLq__cardIcon {
    align-self: center;
    font-size: 2.5rem;
  }

  .BookingCard-module__CVpzLq__cardTitle h3 {
    font-size: 1.25rem;
  }
}

@media (width <= 480px) {
  .BookingCard-module__CVpzLq__bookingCard {
    padding: var(--spacing-md);
  }

  .BookingCard-module__CVpzLq__cardIcon {
    font-size: 2rem;
  }

  .BookingCard-module__CVpzLq__urgentBadge {
    margin-bottom: var(--spacing-sm);
    text-align: center;
    display: inline-block;
    position: static;
  }
}

/*# sourceMappingURL=src_components_BookingCard_module_css_f9ee138c._.single.css.map*/