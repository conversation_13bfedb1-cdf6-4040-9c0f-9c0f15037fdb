{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Header.module.css"], "sourcesContent": [".header {\n  background-color: var(--color-black);\n  border-bottom: 2px solid var(--color-red);\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n  padding: var(--spacing-sm) 0;\n}\n\n.headerContent {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  gap: var(--spacing-md);\n}\n\n.logo {\n  text-decoration: none;\n  color: var(--color-white);\n}\n\n.logo h1 {\n  font-family: var(--font-script);\n  font-size: 2.5rem;\n  margin: 0;\n  color: var(--color-white);\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n}\n\n.tagline {\n  font-family: var(--font-sans);\n  font-size: 0.875rem;\n  color: var(--color-grey);\n  display: block;\n  text-transform: uppercase;\nmargin-top: -0.5rem;\n  font-size: .65rem;\n  display: block;\n  font-weight: 800;\n  margin-left: 3rem;}\n\n.nav {\n  display: flex;\n  gap: var(--spacing-md);\n  align-items: center;\n}\n\n.navLink {\n  color: var(--color-white);\n  text-decoration: none;\n  font-weight: 500;\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--radius-sm);\n  transition: all 0.3s ease;\n  position: relative;\n}\n\n.navLink:hover {\n  color: var(--color-red);\n  background-color: rgba(255, 255, 255, 0.1);\n}\n\n.navLink::after {\n  content: '';\n  position: absolute;\n  bottom: -2px;\n  left: 50%;\n  width: 0;\n  height: 2px;\n  background-color: var(--color-red);\n  transition: all 0.3s ease;\n  transform: translateX(-50%);\n}\n\n.navLink:hover::after {\n  width: 100%;\n}\n\n.ctaButtons {\n  display: flex;\n  gap: var(--spacing-sm);\n  align-items: center;\n}\n\n/* Greedy Navigation Styles */\n.moreDropdown {\n  position: relative;\n  display: inline-block;\n}\n\n.moreButton {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-xs);\n  color: var(--color-white);\n  background: none;\n  border: none;\n  font-weight: 500;\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--radius-sm);\n  transition: all 0.3s ease;\n  cursor: pointer;\n  position: relative;\n  font-size: inherit;\n  font-family: inherit;\n}\n\n.moreButton:hover {\n  color: var(--color-red);\n  background-color: rgba(255, 255, 255, 0.1);\n}\n\n.moreButton::after {\n  content: '';\n  position: absolute;\n  bottom: -2px;\n  left: 50%;\n  width: 0;\n  height: 2px;\n  background-color: var(--color-red);\n  transition: all 0.3s ease;\n  transform: translateX(-50%);\n}\n\n.moreButton:hover::after {\n  width: 100%;\n}\n\n.moreIcon {\n  transition: transform 0.3s ease;\n  flex-shrink: 0;\n}\n\n.moreIconOpen {\n  transform: rotate(180deg);\n}\n\n.moreMenu {\n  position: absolute;\n  top: 100%;\n  right: 0;\n  background-color: var(--color-black);\n  border: 1px solid var(--color-grey);\n  border-radius: var(--radius-sm);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\n  min-width: 160px;\n  z-index: 1001;\n  padding: var(--spacing-xs) 0;\n  margin-top: var(--spacing-xs);\n  animation: fadeInDown 0.2s ease-out;\n}\n\n@keyframes fadeInDown {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.moreMenuItem {\n  display: block;\n  color: var(--color-white);\n  text-decoration: none;\n  padding: var(--spacing-sm) var(--spacing-md);\n  transition: all 0.3s ease;\n  font-weight: 500;\n  white-space: nowrap;\n}\n\n.moreMenuItem:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n  color: var(--color-red);\n}\n\n.mobileMenuButton {\n  display: none;\n  flex-direction: column;\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: var(--spacing-xs);\n  gap: 4px;\n}\n\n.hamburger {\n  width: 25px;\n  height: 3px;\n  background-color: var(--color-white);\n  transition: all 0.3s ease;\n  border-radius: 2px;\n}\n\n.mobileNav {\n  display: none;\n  flex-direction: column;\n  gap: var(--spacing-sm);\n  padding: var(--spacing-md) 0;\n  border-top: 1px solid var(--color-grey);\n  margin-top: var(--spacing-sm);\n}\n\n/* Ensure mobile nav is hidden on desktop */\n@media (min-width: 769px) {\n  .mobileNav,\n  .mobileNavOpen {\n    display: none !important;\n  }\n\n  .mobileMenuButton {\n    display: none !important;\n  }\n}\n\n.mobileNavOpen {\n  display: flex;\n}\n\n.mobileNavLink {\n  color: var(--color-white);\n  text-decoration: none;\n  padding: var(--spacing-sm);\n  border-radius: var(--radius-sm);\n  transition: all 0.3s ease;\n  text-align: center;\n  font-weight: 500;\n}\n\n.mobileNavLink:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n  color: var(--color-red);\n}\n\n.mobileCta {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-sm);\n  margin-top: var(--spacing-md);\n  padding-top: var(--spacing-md);\n  border-top: 1px solid var(--color-grey);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .nav,\n  .ctaButtons {\n    display: none;\n  }\n\n  .mobileMenuButton {\n    display: flex;\n  }\n\n  .logo h1 {\n    font-size: 2rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .logo h1 {\n    font-size: 1.75rem;\n  }\n\n  .headerContent {\n    gap: var(--spacing-sm);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;AASA;;;;;;;AAOA;;;;;AAKA;;;;;;;;AAQA;;;;;;;;;;;AAYA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;;;;;AAYA;;;;AAIA;;;;;;AAOA;;;;;AAKA;;;;;;;;;;;;;;;;;AAiBA;;;;;AAKA;;;;;;;;;;;;AAYA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;;;;;;;;;;;AAeA;;;;;;;;;;;;AAWA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;;;;;;AAUA;EACE;;;;;AAUF;;;;AAIA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;;AAUA;EACE;;;;EAKA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA"}}]}