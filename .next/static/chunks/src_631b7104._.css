/* [project]/src/components/CTAButtons.module.css [app-client] (css) */
.CTAButtons-module__AIDVgW__ctaContainer {
  gap: var(--spacing-md);
  justify-content: center;
  align-items: center;
  display: flex;
}

.CTAButtons-module__AIDVgW__horizontal {
  flex-flow: wrap;
}

.CTAButtons-module__AIDVgW__vertical {
  flex-direction: column;
  align-items: stretch;
}

.CTAButtons-module__AIDVgW__small .CTAButtons-module__AIDVgW__btn {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: .875rem;
}

@media (width <= 768px) {
  .CTAButtons-module__AIDVgW__horizontal {
    flex-direction: column;
    align-items: stretch;
  }

  .CTAButtons-module__AIDVgW__ctaContainer {
    gap: var(--spacing-sm);
  }
}

@media (width <= 480px) {
  .CTAButtons-module__AIDVgW__ctaContainer .CTAButtons-module__AIDVgW__btn {
    text-align: center;
    width: 100%;
  }
}


/* [project]/src/app/about/page.module.css [app-client] (css) */
.page-module__NfDiEG__aboutPage {
  min-height: 100vh;
}

.page-module__NfDiEG__hero {
  padding: var(--spacing-2xl) 0;
  background: linear-gradient(135deg, var(--color-black) 0%, #1a1a1a 100%);
  text-align: center;
}

.page-module__NfDiEG__heroContent h1 {
  color: var(--color-white);
  margin-bottom: var(--spacing-sm);
  font-size: 4rem;
}

.page-module__NfDiEG__heroTagline {
  color: var(--color-red);
  margin-bottom: var(--spacing-md);
  font-size: 1.5rem;
  font-style: italic;
  font-weight: 600;
}

.page-module__NfDiEG__heroDescription {
  color: var(--color-grey);
  max-width: 600px;
  margin: 0 auto;
  font-size: 1.25rem;
  line-height: 1.6;
}

.page-module__NfDiEG__story {
  padding: var(--spacing-2xl) 0;
  background-color: #111;
}

.page-module__NfDiEG__storyContent {
  gap: var(--spacing-2xl);
  grid-template-columns: 2fr 1fr;
  align-items: center;
  display: grid;
}

.page-module__NfDiEG__storyText h2 {
  color: var(--color-white);
  margin-bottom: var(--spacing-lg);
}

.page-module__NfDiEG__storyText p {
  color: var(--color-grey);
  margin-bottom: var(--spacing-md);
  font-size: 1.125rem;
  line-height: 1.7;
}

.page-module__NfDiEG__storyText p:last-child {
  margin-bottom: 0;
}

.page-module__NfDiEG__experienceBadge {
  background: linear-gradient(135deg, var(--color-red), #b91c1c);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  text-align: center;
  color: var(--color-white);
  box-shadow: var(--shadow-lg);
}

.page-module__NfDiEG__experienceBadge h3 {
  margin-bottom: var(--spacing-sm);
  text-shadow: 2px 2px 4px #0000004d;
  font-size: 5rem;
  font-weight: bold;
  line-height: 1;
}

.page-module__NfDiEG__experienceBadge p {
  text-transform: uppercase;
  letter-spacing: .1em;
  margin: 0;
  font-size: 1.25rem;
}

.page-module__NfDiEG__values {
  padding: var(--spacing-2xl) 0;
  background-color: var(--color-black);
}

.page-module__NfDiEG__valueCard {
  border: 2px solid var(--color-grey);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  text-align: center;
  background-color: #111;
  height: 100%;
  transition: all .3s;
}

.page-module__NfDiEG__valueCard:hover {
  border-color: var(--color-red);
  box-shadow: var(--shadow-lg);
  transform: translateY(-5px);
}

.page-module__NfDiEG__valueIcon {
  margin-bottom: var(--spacing-md);
  font-size: 3rem;
}

.page-module__NfDiEG__valueCard h3 {
  color: var(--color-white);
  margin-bottom: var(--spacing-sm);
}

.page-module__NfDiEG__valueCard p {
  color: var(--color-grey);
  line-height: 1.6;
}

.page-module__NfDiEG__difference {
  padding: var(--spacing-2xl) 0;
  background-color: #111;
}

.page-module__NfDiEG__differenceContent {
  gap: var(--spacing-2xl);
  grid-template-columns: 2fr 1fr;
  align-items: start;
  display: grid;
}

.page-module__NfDiEG__differenceText h2 {
  color: var(--color-white);
  margin-bottom: var(--spacing-lg);
}

.page-module__NfDiEG__differenceList {
  margin: 0;
  padding: 0;
  list-style: none;
}

.page-module__NfDiEG__differenceList li {
  color: var(--color-grey);
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid #ffffff1a;
  font-size: 1.125rem;
  line-height: 1.6;
}

.page-module__NfDiEG__differenceList li:last-child {
  border-bottom: none;
}

.page-module__NfDiEG__differenceList strong {
  color: var(--color-red);
  font-weight: 600;
}

.page-module__NfDiEG__differenceStats {
  gap: var(--spacing-md);
  flex-direction: column;
  display: flex;
}

.page-module__NfDiEG__statCard {
  background: linear-gradient(135deg, var(--color-black), #2a2a2a);
  border: 2px solid var(--color-red);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  text-align: center;
  color: var(--color-white);
}

.page-module__NfDiEG__statCard h3 {
  margin-bottom: var(--spacing-xs);
  color: var(--color-red);
  font-size: 2.5rem;
  font-weight: bold;
  line-height: 1;
}

.page-module__NfDiEG__statCard p {
  text-transform: uppercase;
  letter-spacing: .1em;
  margin: 0;
  font-size: 1rem;
}

.page-module__NfDiEG__cta {
  padding: var(--spacing-2xl) 0;
  background: linear-gradient(135deg, var(--color-red), #b91c1c);
  text-align: center;
}

.page-module__NfDiEG__ctaContent h2 {
  color: var(--color-white);
  margin-bottom: var(--spacing-md);
  font-size: 2.5rem;
}

.page-module__NfDiEG__ctaContent p {
  color: var(--color-white);
  margin-bottom: var(--spacing-xl);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  font-size: 1.25rem;
  line-height: 1.6;
}

@media (width <= 768px) {
  .page-module__NfDiEG__heroContent h1 {
    font-size: 3rem;
  }

  .page-module__NfDiEG__heroTagline {
    font-size: 1.25rem;
  }

  .page-module__NfDiEG__storyContent {
    gap: var(--spacing-xl);
    grid-template-columns: 1fr;
  }

  .page-module__NfDiEG__experienceBadge h3 {
    font-size: 4rem;
  }

  .page-module__NfDiEG__differenceContent {
    gap: var(--spacing-xl);
    grid-template-columns: 1fr;
  }

  .page-module__NfDiEG__differenceStats {
    flex-direction: row;
    justify-content: space-between;
  }

  .page-module__NfDiEG__statCard {
    margin: 0 var(--spacing-xs);
    flex: 1;
  }

  .page-module__NfDiEG__ctaContent h2 {
    font-size: 2rem;
  }
}

@media (width <= 480px) {
  .page-module__NfDiEG__heroContent h1 {
    font-size: 2.5rem;
  }

  .page-module__NfDiEG__experienceBadge {
    padding: var(--spacing-xl);
  }

  .page-module__NfDiEG__experienceBadge h3 {
    font-size: 3rem;
  }

  .page-module__NfDiEG__differenceStats {
    flex-direction: column;
  }

  .page-module__NfDiEG__statCard {
    margin: 0;
  }

  .page-module__NfDiEG__statCard h3 {
    font-size: 2rem;
  }

  .page-module__NfDiEG__valueCard {
    padding: var(--spacing-lg);
  }
}


/*# sourceMappingURL=src_631b7104._.css.map*/