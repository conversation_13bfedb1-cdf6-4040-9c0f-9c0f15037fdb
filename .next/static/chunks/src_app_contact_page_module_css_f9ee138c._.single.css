/* [project]/src/app/contact/page.module.css [app-client] (css) */
.page-module__OSLHOG__contactPage {
  min-height: 100vh;
}

.page-module__OSLHOG__hero {
  padding: var(--spacing-2xl) 0;
  background: linear-gradient(135deg, var(--color-black) 0%, #1a1a1a 100%);
  text-align: center;
}

.page-module__OSLHOG__heroContent h1 {
  color: var(--color-white);
  margin-bottom: var(--spacing-md);
  font-size: 4rem;
}

.page-module__OSLHOG__heroDescription {
  color: var(--color-grey);
  max-width: 700px;
  margin: 0 auto;
  font-size: 1.25rem;
  line-height: 1.6;
}

.page-module__OSLHOG__contactInfo {
  padding: var(--spacing-2xl) 0;
  background-color: #111;
}

.page-module__OSLHOG__contactGrid {
  gap: var(--spacing-2xl);
  grid-template-columns: 2fr 1fr;
  align-items: start;
  display: grid;
}

.page-module__OSLHOG__contactDetails h2 {
  color: var(--color-white);
  margin-bottom: var(--spacing-xl);
}

.page-module__OSLHOG__contactItem {
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background-color: var(--color-black);
  border: 2px solid var(--color-grey);
  border-radius: var(--radius-lg);
  transition: all .3s;
  display: flex;
}

.page-module__OSLHOG__contactItem:hover {
  border-color: var(--color-red);
  transform: translateY(-2px);
}

.page-module__OSLHOG__contactIcon {
  flex-shrink: 0;
  font-size: 2rem;
  line-height: 1;
}

.page-module__OSLHOG__contactText h3 {
  color: var(--color-white);
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 1.25rem;
}

.page-module__OSLHOG__contactText p {
  margin: 0 0 var(--spacing-xs) 0;
}

.page-module__OSLHOG__contactLink {
  color: var(--color-red);
  font-size: 1.125rem;
  font-weight: 600;
  text-decoration: none;
  transition: color .3s;
}

.page-module__OSLHOG__contactLink:hover {
  color: var(--color-white);
}

.page-module__OSLHOG__contactNote {
  color: var(--color-grey);
  font-size: .875rem;
  font-style: italic;
}

.page-module__OSLHOG__businessInfo {
  gap: var(--spacing-lg);
  flex-direction: column;
  display: flex;
}

.page-module__OSLHOG__hoursCard, .page-module__OSLHOG__servicesCard {
  background-color: var(--color-black);
  border: 2px solid var(--color-red);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
}

.page-module__OSLHOG__hoursCard h3, .page-module__OSLHOG__servicesCard h3 {
  color: var(--color-white);
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.page-module__OSLHOG__hours {
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  flex-direction: column;
  display: flex;
}

.page-module__OSLHOG__hourItem {
  color: var(--color-grey);
  padding: var(--spacing-xs) 0;
  border-bottom: 1px solid #ffffff1a;
  justify-content: space-between;
  display: flex;
}

.page-module__OSLHOG__hourItem:last-child {
  border-bottom: none;
}

.page-module__OSLHOG__emergencyNote {
  color: var(--color-red);
  text-align: center;
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  background-color: #dc26261a;
  margin: 0;
  font-weight: 600;
}

.page-module__OSLHOG__servicesList {
  margin: 0;
  padding: 0;
  list-style: none;
}

.page-module__OSLHOG__servicesList li {
  color: var(--color-grey);
  padding: var(--spacing-xs) 0;
  border-bottom: 1px solid #ffffff1a;
  transition: color .3s;
}

.page-module__OSLHOG__servicesList li:hover {
  color: var(--color-white);
}

.page-module__OSLHOG__servicesList li:last-child {
  border-bottom: none;
}

.page-module__OSLHOG__servicesList li:before {
  content: "✓ ";
  color: var(--color-red);
  font-weight: bold;
}

.page-module__OSLHOG__mapSection {
  padding: var(--spacing-2xl) 0;
  background-color: var(--color-black);
}

.page-module__OSLHOG__mapContainer {
  margin-bottom: var(--spacing-xl);
}

.page-module__OSLHOG__mapPlaceholder {
  border: 2px solid var(--color-red);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  text-align: center;
  background: linear-gradient(135deg, #111, #2a2a2a);
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  display: flex;
}

.page-module__OSLHOG__mapPlaceholder h3 {
  color: var(--color-white);
  margin-bottom: var(--spacing-md);
  font-size: 2rem;
}

.page-module__OSLHOG__mapPlaceholder p {
  color: var(--color-grey);
  margin-bottom: var(--spacing-sm);
  font-size: 1.125rem;
}

.page-module__OSLHOG__serviceAreas {
  text-align: center;
}

.page-module__OSLHOG__serviceAreas h4 {
  color: var(--color-white);
  margin-bottom: var(--spacing-md);
}

.page-module__OSLHOG__areasList {
  gap: var(--spacing-sm);
  flex-wrap: wrap;
  justify-content: center;
  display: flex;
}

.page-module__OSLHOG__areasList span {
  color: var(--color-grey);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  border: 1px solid var(--color-grey);
  background-color: #111;
  transition: all .3s;
}

.page-module__OSLHOG__areasList span:hover {
  background-color: var(--color-red);
  color: var(--color-white);
  border-color: var(--color-red);
}

.page-module__OSLHOG__emergency {
  padding: var(--spacing-xl) 0;
  background: linear-gradient(135deg, var(--color-red), #b91c1c);
  text-align: center;
}

.page-module__OSLHOG__emergencyContent h2 {
  color: var(--color-white);
  margin-bottom: var(--spacing-md);
  font-size: 2.5rem;
}

.page-module__OSLHOG__emergencyContent p {
  color: var(--color-white);
  margin-bottom: var(--spacing-xl);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  font-size: 1.25rem;
  line-height: 1.6;
}

.page-module__OSLHOG__cta {
  padding: var(--spacing-2xl) 0;
  text-align: center;
  background-color: #111;
}

.page-module__OSLHOG__ctaContent h2 {
  color: var(--color-white);
  margin-bottom: var(--spacing-md);
  font-size: 2.5rem;
}

.page-module__OSLHOG__ctaContent p {
  color: var(--color-grey);
  margin-bottom: var(--spacing-xl);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  font-size: 1.25rem;
  line-height: 1.6;
}

@media (width <= 768px) {
  .page-module__OSLHOG__heroContent h1 {
    font-size: 3rem;
  }

  .page-module__OSLHOG__heroDescription {
    font-size: 1.125rem;
  }

  .page-module__OSLHOG__contactGrid {
    gap: var(--spacing-xl);
    grid-template-columns: 1fr;
  }

  .page-module__OSLHOG__contactItem {
    padding: var(--spacing-md);
  }

  .page-module__OSLHOG__hoursCard, .page-module__OSLHOG__servicesCard {
    padding: var(--spacing-lg);
  }

  .page-module__OSLHOG__mapPlaceholder {
    padding: var(--spacing-xl);
    min-height: 200px;
  }

  .page-module__OSLHOG__emergencyContent h2, .page-module__OSLHOG__ctaContent h2 {
    font-size: 2rem;
  }
}

@media (width <= 480px) {
  .page-module__OSLHOG__heroContent h1 {
    font-size: 2.5rem;
  }

  .page-module__OSLHOG__contactItem {
    text-align: center;
    gap: var(--spacing-sm);
    flex-direction: column;
  }

  .page-module__OSLHOG__contactIcon {
    align-self: center;
  }

  .page-module__OSLHOG__mapPlaceholder {
    padding: var(--spacing-lg);
  }

  .page-module__OSLHOG__mapPlaceholder h3 {
    font-size: 1.5rem;
  }

  .page-module__OSLHOG__areasList {
    gap: var(--spacing-xs);
  }

  .page-module__OSLHOG__areasList span {
    font-size: .875rem;
  }
}

/*# sourceMappingURL=src_app_contact_page_module_css_f9ee138c._.single.css.map*/