/* [project]/src/components/Footer.module.css [app-client] (css) */
.Footer-module__S6Hkya__footer {
  background-color: var(--color-black);
  border-top: 2px solid var(--color-red);
  margin-top: var(--spacing-2xl);
  padding: var(--spacing-2xl) 0 var(--spacing-lg);
}

.Footer-module__S6Hkya__footerContent {
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  display: grid;
}

.Footer-module__S6Hkya__footerSection h3 {
  font-family: var(--font-script);
  color: var(--color-white);
  margin-bottom: var(--spacing-sm);
  font-size: 2rem;
}

.Footer-module__S6Hkya__footerSection h4 {
  font-family: var(--font-script);
  color: var(--color-red);
  margin-bottom: var(--spacing-sm);
  font-size: 1.5rem;
}

.Footer-module__S6Hkya__tagline {
  color: var(--color-red);
  margin-bottom: var(--spacing-sm);
  font-style: italic;
  font-weight: 600;
}

.Footer-module__S6Hkya__footerSection p {
  color: var(--color-grey);
  margin-bottom: var(--spacing-sm);
  line-height: 1.6;
}

.Footer-module__S6Hkya__footerNav {
  gap: var(--spacing-xs);
  flex-direction: column;
  display: flex;
}

.Footer-module__S6Hkya__footerLink {
  color: var(--color-grey);
  padding: var(--spacing-xs) 0;
  border-bottom: 1px solid #0000;
  text-decoration: none;
  transition: color .3s;
}

.Footer-module__S6Hkya__footerLink:hover {
  color: var(--color-white);
  border-bottom-color: var(--color-red);
}

.Footer-module__S6Hkya__contactInfo {
  gap: var(--spacing-xs);
  flex-direction: column;
  display: flex;
}

.Footer-module__S6Hkya__contactInfo p {
  margin-bottom: 0;
}

.Footer-module__S6Hkya__contactLink {
  color: var(--color-white);
  text-decoration: none;
  transition: color .3s;
}

.Footer-module__S6Hkya__contactLink:hover {
  color: var(--color-red);
}

.Footer-module__S6Hkya__servicesList {
  margin: 0;
  padding: 0;
  list-style: none;
}

.Footer-module__S6Hkya__servicesList li {
  color: var(--color-grey);
  padding: var(--spacing-xs) 0;
  border-bottom: 1px solid #ffffff1a;
  transition: color .3s;
}

.Footer-module__S6Hkya__servicesList li:hover {
  color: var(--color-white);
}

.Footer-module__S6Hkya__servicesList li:last-child {
  border-bottom: none;
}

.Footer-module__S6Hkya__footerBottom {
  border-top: 1px solid var(--color-grey);
  padding-top: var(--spacing-lg);
}

.Footer-module__S6Hkya__footerBottomContent {
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-md);
  display: flex;
}

.Footer-module__S6Hkya__footerBottomContent p {
  color: var(--color-grey);
  margin: 0;
}

.Footer-module__S6Hkya__footerCta {
  gap: var(--spacing-sm);
  align-items: center;
  display: flex;
}

@media (width <= 768px) {
  .Footer-module__S6Hkya__footerContent {
    gap: var(--spacing-lg);
    grid-template-columns: 1fr;
  }

  .Footer-module__S6Hkya__footerBottomContent {
    text-align: center;
    gap: var(--spacing-md);
    flex-direction: column;
  }

  .Footer-module__S6Hkya__footerCta {
    justify-content: center;
  }
}

@media (width <= 480px) {
  .Footer-module__S6Hkya__footer {
    padding: var(--spacing-xl) 0 var(--spacing-md);
  }

  .Footer-module__S6Hkya__footerContent {
    gap: var(--spacing-md);
  }

  .Footer-module__S6Hkya__footerSection h3 {
    font-size: 1.75rem;
  }

  .Footer-module__S6Hkya__footerSection h4 {
    font-size: 1.25rem;
  }

  .Footer-module__S6Hkya__footerCta {
    flex-direction: column;
    width: 100%;
  }

  .Footer-module__S6Hkya__footerCta .Footer-module__S6Hkya__btn {
    text-align: center;
    width: 100%;
  }
}

/*# sourceMappingURL=src_components_Footer_module_css_f9ee138c._.single.css.map*/