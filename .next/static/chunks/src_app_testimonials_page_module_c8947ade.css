/* [project]/src/app/testimonials/page.module.css [app-client] (css) */
.page-module__hPV2JG__testimonialsPage {
  min-height: 100vh;
}

.page-module__hPV2JG__hero {
  padding: var(--spacing-2xl) 0;
  background: linear-gradient(135deg, var(--color-black) 0%, #1a1a1a 100%);
  text-align: center;
}

.page-module__hPV2JG__heroContent h1 {
  color: var(--color-white);
  margin-bottom: var(--spacing-md);
  font-size: 4rem;
}

.page-module__hPV2JG__heroDescription {
  color: var(--color-grey);
  max-width: 700px;
  margin: 0 auto;
  font-size: 1.25rem;
  line-height: 1.6;
}

.page-module__hPV2JG__stats {
  padding: var(--spacing-xl) 0;
  background-color: #111;
}

.page-module__hPV2JG__statsGrid {
  gap: var(--spacing-lg);
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  display: grid;
}

.page-module__hPV2JG__statCard {
  background-color: var(--color-black);
  border: 2px solid var(--color-red);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  text-align: center;
  transition: all .3s;
}

.page-module__hPV2JG__statCard:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-5px);
}

.page-module__hPV2JG__statCard h3 {
  color: var(--color-red);
  margin-bottom: var(--spacing-xs);
  font-size: 2.5rem;
  font-weight: bold;
}

.page-module__hPV2JG__statCard p {
  color: var(--color-grey);
  text-transform: uppercase;
  letter-spacing: .1em;
  margin: 0;
  font-size: .875rem;
}

.page-module__hPV2JG__testimonials {
  padding: var(--spacing-2xl) 0;
  background-color: var(--color-black);
}

.page-module__hPV2JG__testimonialsGrid {
  gap: var(--spacing-xl);
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  display: grid;
}

.page-module__hPV2JG__testimonialCard {
  border: 2px solid var(--color-grey);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  background-color: #111;
  transition: all .3s;
  position: relative;
  overflow: hidden;
}

.page-module__hPV2JG__testimonialCard:before {
  content: "";
  background: linear-gradient(90deg, var(--color-red), #b91c1c);
  height: 4px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.page-module__hPV2JG__testimonialCard:hover {
  border-color: var(--color-red);
  box-shadow: var(--shadow-lg);
  transform: translateY(-5px);
}

.page-module__hPV2JG__cardHeader {
  margin-bottom: var(--spacing-md);
  justify-content: space-between;
  align-items: flex-start;
  display: flex;
}

.page-module__hPV2JG__serviceTicket {
  gap: var(--spacing-xs);
  flex-direction: column;
  display: flex;
}

.page-module__hPV2JG__ticketId {
  color: var(--color-red);
  font-family: Courier New, monospace;
  font-size: .875rem;
  font-weight: bold;
}

.page-module__hPV2JG__ticketDate {
  color: var(--color-grey);
  text-transform: uppercase;
  letter-spacing: .1em;
  font-size: .75rem;
}

.page-module__hPV2JG__rating {
  color: gold;
  font-size: 1.25rem;
  line-height: 1;
}

.page-module__hPV2JG__cardContent {
  margin-bottom: var(--spacing-lg);
}

.page-module__hPV2JG__review {
  color: var(--color-grey);
  margin: 0;
  font-size: 1.125rem;
  font-style: italic;
  line-height: 1.6;
  position: relative;
}

.page-module__hPV2JG__review:before {
  content: "\"";
  color: var(--color-red);
  opacity: .5;
  font-family: serif;
  font-size: 3rem;
  position: absolute;
  top: -10px;
  left: -20px;
}

.page-module__hPV2JG__cardFooter {
  justify-content: space-between;
  align-items: flex-end;
  gap: var(--spacing-md);
  display: flex;
}

.page-module__hPV2JG__customerInfo h4 {
  color: var(--color-white);
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 1.125rem;
}

.page-module__hPV2JG__vehicle {
  color: var(--color-grey);
  margin: 0 0 var(--spacing-xs) 0;
  font-size: .875rem;
}

.page-module__hPV2JG__location {
  color: var(--color-red);
  text-transform: uppercase;
  letter-spacing: .1em;
  margin: 0;
  font-size: .75rem;
}

.page-module__hPV2JG__serviceInfo {
  text-align: right;
}

.page-module__hPV2JG__serviceType {
  background-color: var(--color-red);
  color: var(--color-white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  text-transform: uppercase;
  letter-spacing: .1em;
  font-size: .75rem;
  font-weight: 600;
}

.page-module__hPV2JG__cta {
  padding: var(--spacing-2xl) 0;
  background: linear-gradient(135deg, var(--color-red), #b91c1c);
  text-align: center;
}

.page-module__hPV2JG__ctaContent h2 {
  color: var(--color-white);
  margin-bottom: var(--spacing-md);
  font-size: 2.5rem;
}

.page-module__hPV2JG__ctaContent p {
  color: var(--color-white);
  margin-bottom: var(--spacing-xl);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  font-size: 1.25rem;
  line-height: 1.6;
}

.page-module__hPV2JG__ctaButtons {
  gap: var(--spacing-md);
  justify-content: center;
  align-items: center;
  display: flex;
}

@media (width <= 768px) {
  .page-module__hPV2JG__heroContent h1 {
    font-size: 3rem;
  }

  .page-module__hPV2JG__heroDescription {
    font-size: 1.125rem;
  }

  .page-module__hPV2JG__testimonialsGrid {
    gap: var(--spacing-lg);
    grid-template-columns: 1fr;
  }

  .page-module__hPV2JG__testimonialCard {
    padding: var(--spacing-lg);
  }

  .page-module__hPV2JG__cardFooter {
    align-items: flex-start;
    gap: var(--spacing-sm);
    flex-direction: column;
  }

  .page-module__hPV2JG__serviceInfo {
    text-align: left;
    align-self: stretch;
  }

  .page-module__hPV2JG__ctaButtons {
    flex-direction: column;
    align-items: stretch;
  }

  .page-module__hPV2JG__ctaContent h2 {
    font-size: 2rem;
  }
}

@media (width <= 480px) {
  .page-module__hPV2JG__heroContent h1 {
    font-size: 2.5rem;
  }

  .page-module__hPV2JG__statsGrid {
    gap: var(--spacing-sm);
    grid-template-columns: repeat(2, 1fr);
  }

  .page-module__hPV2JG__statCard {
    padding: var(--spacing-md);
  }

  .page-module__hPV2JG__statCard h3 {
    font-size: 2rem;
  }

  .page-module__hPV2JG__testimonialCard {
    padding: var(--spacing-md);
  }

  .page-module__hPV2JG__review {
    font-size: 1rem;
  }

  .page-module__hPV2JG__review:before {
    font-size: 2rem;
    top: -5px;
    left: -15px;
  }
}


/*# sourceMappingURL=src_app_testimonials_page_module_c8947ade.css.map*/