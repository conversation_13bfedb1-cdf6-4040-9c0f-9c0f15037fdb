{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/about/page.module.css"], "sourcesContent": [".aboutPage {\n  min-height: 100vh;\n}\n\n/* Hero Section */\n.hero {\n  padding: var(--spacing-2xl) 0;\n  background: linear-gradient(135deg, var(--color-black) 0%, #1a1a1a 100%);\n  text-align: center;\n}\n\n.heroContent h1 {\n  font-size: 4rem;\n  color: var(--color-white);\n  margin-bottom: var(--spacing-sm);\n}\n\n.heroTagline {\n  font-size: 1.5rem;\n  color: var(--color-red);\n  font-weight: 600;\n  margin-bottom: var(--spacing-md);\n  font-style: italic;\n}\n\n.heroDescription {\n  font-size: 1.25rem;\n  color: var(--color-grey);\n  max-width: 600px;\n  margin: 0 auto;\n  line-height: 1.6;\n}\n\n/* Story Section */\n.story {\n  padding: var(--spacing-2xl) 0;\n  background-color: #111111;\n}\n\n.storyContent {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: var(--spacing-2xl);\n  align-items: center;\n}\n\n.storyText h2 {\n  color: var(--color-white);\n  margin-bottom: var(--spacing-lg);\n}\n\n.storyText p {\n  color: var(--color-grey);\n  font-size: 1.125rem;\n  line-height: 1.7;\n  margin-bottom: var(--spacing-md);\n}\n\n.storyText p:last-child {\n  margin-bottom: 0;\n}\n\n.experienceBadge {\n  background: linear-gradient(135deg, var(--color-red), #b91c1c);\n  border-radius: var(--radius-lg);\n  padding: var(--spacing-2xl);\n  text-align: center;\n  color: var(--color-white);\n  box-shadow: var(--shadow-lg);\n}\n\n.experienceBadge h3 {\n  font-size: 5rem;\n  font-weight: bold;\n  line-height: 1;\n  margin-bottom: var(--spacing-sm);\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);\n}\n\n.experienceBadge p {\n  font-size: 1.25rem;\n  margin: 0;\n  text-transform: uppercase;\n  letter-spacing: 0.1em;\n}\n\n/* Values Section */\n.values {\n  padding: var(--spacing-2xl) 0;\n  background-color: var(--color-black);\n}\n\n.valueCard {\n  background-color: #111111;\n  border: 2px solid var(--color-grey);\n  border-radius: var(--radius-lg);\n  padding: var(--spacing-xl);\n  text-align: center;\n  transition: all 0.3s ease;\n  height: 100%;\n}\n\n.valueCard:hover {\n  border-color: var(--color-red);\n  transform: translateY(-5px);\n  box-shadow: var(--shadow-lg);\n}\n\n.valueIcon {\n  font-size: 3rem;\n  margin-bottom: var(--spacing-md);\n}\n\n.valueCard h3 {\n  color: var(--color-white);\n  margin-bottom: var(--spacing-sm);\n}\n\n.valueCard p {\n  color: var(--color-grey);\n  line-height: 1.6;\n}\n\n/* Difference Section */\n.difference {\n  padding: var(--spacing-2xl) 0;\n  background-color: #111111;\n}\n\n.differenceContent {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: var(--spacing-2xl);\n  align-items: start;\n}\n\n.differenceText h2 {\n  color: var(--color-white);\n  margin-bottom: var(--spacing-lg);\n}\n\n.differenceList {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.differenceList li {\n  color: var(--color-grey);\n  padding: var(--spacing-md) 0;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  font-size: 1.125rem;\n  line-height: 1.6;\n}\n\n.differenceList li:last-child {\n  border-bottom: none;\n}\n\n.differenceList strong {\n  color: var(--color-red);\n  font-weight: 600;\n}\n\n.differenceStats {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-md);\n}\n\n.statCard {\n  background: linear-gradient(135deg, var(--color-black), #2a2a2a);\n  border: 2px solid var(--color-red);\n  border-radius: var(--radius-lg);\n  padding: var(--spacing-lg);\n  text-align: center;\n  color: var(--color-white);\n}\n\n.statCard h3 {\n  font-size: 2.5rem;\n  font-weight: bold;\n  line-height: 1;\n  margin-bottom: var(--spacing-xs);\n  color: var(--color-red);\n}\n\n.statCard p {\n  font-size: 1rem;\n  margin: 0;\n  text-transform: uppercase;\n  letter-spacing: 0.1em;\n}\n\n/* CTA Section */\n.cta {\n  padding: var(--spacing-2xl) 0;\n  background: linear-gradient(135deg, var(--color-red), #b91c1c);\n  text-align: center;\n}\n\n.ctaContent h2 {\n  color: var(--color-white);\n  font-size: 2.5rem;\n  margin-bottom: var(--spacing-md);\n}\n\n.ctaContent p {\n  color: var(--color-white);\n  font-size: 1.25rem;\n  margin-bottom: var(--spacing-xl);\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .heroContent h1 {\n    font-size: 3rem;\n  }\n\n  .heroTagline {\n    font-size: 1.25rem;\n  }\n\n  .storyContent {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-xl);\n  }\n\n  .experienceBadge h3 {\n    font-size: 4rem;\n  }\n\n  .differenceContent {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-xl);\n  }\n\n  .differenceStats {\n    flex-direction: row;\n    justify-content: space-between;\n  }\n\n  .statCard {\n    flex: 1;\n    margin: 0 var(--spacing-xs);\n  }\n\n  .ctaContent h2 {\n    font-size: 2rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .heroContent h1 {\n    font-size: 2.5rem;\n  }\n\n  .experienceBadge {\n    padding: var(--spacing-xl);\n  }\n\n  .experienceBadge h3 {\n    font-size: 3rem;\n  }\n\n  .differenceStats {\n    flex-direction: column;\n  }\n\n  .statCard {\n    margin: 0;\n  }\n\n  .statCard h3 {\n    font-size: 2rem;\n  }\n\n  .valueCard {\n    padding: var(--spacing-lg);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;;AASA;;;;;AAKA;;;;;;;AAOA;;;;;AAKA;;;;;;;AAOA;;;;AAIA;;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;AAQA;;;;;AAKA;;;;;;;;;;AAUA;;;;;;AAMA;;;;;AAKA;;;;;AAKA;;;;;AAMA;;;;;AAKA;;;;;;;AAOA;;;;;AAKA;;;;;;AAMA;;;;;;;;AAQA;;;;AAIA;;;;;AAKA;;;;;;AAMA;;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;AAQA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;AAWA;EACE;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA"}}]}