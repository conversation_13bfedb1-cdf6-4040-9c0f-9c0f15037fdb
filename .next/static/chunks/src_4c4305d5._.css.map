{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/src/components/BookingCard.module.css"], "sourcesContent": [".bookingCard {\n  background-color: var(--color-black);\n  border: 2px solid var(--color-grey);\n  border-radius: var(--radius-lg);\n  padding: var(--spacing-xl);\n  transition: all 0.3s ease;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  overflow: hidden;\n}\n\n.bookingCard:hover {\n  border-color: var(--color-red);\n  transform: translateY(-5px);\n  box-shadow: var(--shadow-lg);\n}\n\n.urgent {\n  border-color: var(--color-red);\n  background: linear-gradient(135deg, var(--color-black), #1a0000);\n}\n\n.urgent:hover {\n  border-color: var(--color-white);\n  box-shadow: 0 10px 25px rgba(220, 38, 38, 0.3);\n}\n\n.cardHeader {\n  display: flex;\n  align-items: flex-start;\n  gap: var(--spacing-md);\n  margin-bottom: var(--spacing-md);\n}\n\n.cardIcon {\n  font-size: 3rem;\n  flex-shrink: 0;\n  line-height: 1;\n}\n\n.cardTitle h3 {\n  color: var(--color-white);\n  margin: 0 0 var(--spacing-xs) 0;\n  font-size: 1.5rem;\n  line-height: 1.2;\n}\n\n.duration {\n  color: var(--color-red);\n  font-size: 0.875rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.1em;\n}\n\n.cardContent {\n  flex-grow: 1;\n  margin-bottom: var(--spacing-lg);\n}\n\n.description {\n  color: var(--color-grey);\n  line-height: 1.6;\n  margin: 0;\n  font-size: 1rem;\n}\n\n.cardFooter {\n  margin-top: auto;\n}\n\n.bookingButton {\n  width: 100%;\n  text-align: center;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n}\n\n.urgentBadge {\n  position: absolute;\n  top: var(--spacing-sm);\n  right: var(--spacing-sm);\n  background-color: var(--color-red);\n  color: var(--color-white);\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--radius-sm);\n  font-size: 0.75rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.1em;\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% {\n    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0.7);\n  }\n  70% {\n    box-shadow: 0 0 0 10px rgba(220, 38, 38, 0);\n  }\n  100% {\n    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0);\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .bookingCard {\n    padding: var(--spacing-lg);\n  }\n\n  .cardHeader {\n    flex-direction: column;\n    text-align: center;\n    gap: var(--spacing-sm);\n  }\n\n  .cardIcon {\n    font-size: 2.5rem;\n    align-self: center;\n  }\n\n  .cardTitle h3 {\n    font-size: 1.25rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .bookingCard {\n    padding: var(--spacing-md);\n  }\n\n  .cardIcon {\n    font-size: 2rem;\n  }\n\n  .urgentBadge {\n    position: static;\n    margin-bottom: var(--spacing-sm);\n    text-align: center;\n    display: inline-block;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;AAaA;;;;;;AAMA;;;;;AAKA;;;;;AAKA;;;;;;;AAOA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;AAQA;;;;;AAKA;;;;;;;AAOA;;;;AAIA;;;;;;;;AAQA;;;;;;;;;;;;;;;AAeA;;;;;;;;;;;;;;AAaA;EACE;;;;EAIA;;;;;;EAMA;;;;;EAKA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/src/app/bookings/page.module.css"], "sourcesContent": [".bookingsPage {\n  min-height: 100vh;\n}\n\n/* Hero Section */\n.hero {\n  padding: var(--spacing-2xl) 0;\n  background: linear-gradient(135deg, var(--color-black) 0%, #1a1a1a 100%);\n  text-align: center;\n}\n\n.heroContent h1 {\n  font-size: 4rem;\n  color: var(--color-white);\n  margin-bottom: var(--spacing-md);\n}\n\n.heroDescription {\n  font-size: 1.25rem;\n  color: var(--color-grey);\n  max-width: 700px;\n  margin: 0 auto;\n  line-height: 1.6;\n}\n\n/* Booking Cards */\n.bookingCards {\n  padding: var(--spacing-2xl) 0;\n  background-color: #111111;\n}\n\n.cardsGrid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n  gap: var(--spacing-xl);\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n/* Booking Info */\n.bookingInfo {\n  padding: var(--spacing-2xl) 0;\n  background-color: var(--color-black);\n}\n\n.infoContent {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: var(--spacing-2xl);\n  align-items: start;\n}\n\n.infoText h2 {\n  color: var(--color-white);\n  margin-bottom: var(--spacing-lg);\n}\n\n.stepsList {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n  counter-reset: step-counter;\n}\n\n.stepsList li {\n  color: var(--color-grey);\n  padding: var(--spacing-md) 0;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  font-size: 1.125rem;\n  line-height: 1.6;\n  counter-increment: step-counter;\n  position: relative;\n  padding-left: var(--spacing-xl);\n}\n\n.stepsList li::before {\n  content: counter(step-counter);\n  position: absolute;\n  left: 0;\n  top: var(--spacing-md);\n  width: 2rem;\n  height: 2rem;\n  background-color: var(--color-red);\n  color: var(--color-white);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: 0.875rem;\n}\n\n.stepsList li:last-child {\n  border-bottom: none;\n}\n\n.stepsList strong {\n  color: var(--color-red);\n  font-weight: 600;\n}\n\n.contactInfo {\n  background-color: #111111;\n  border: 2px solid var(--color-grey);\n  border-radius: var(--radius-lg);\n  padding: var(--spacing-xl);\n  text-align: center;\n}\n\n.contactInfo h3 {\n  color: var(--color-white);\n  margin-bottom: var(--spacing-md);\n}\n\n.contactInfo p {\n  color: var(--color-grey);\n  line-height: 1.6;\n  margin-bottom: var(--spacing-lg);\n}\n\n.contactOptions {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-sm);\n}\n\n/* Emergency Section */\n.emergency {\n  padding: var(--spacing-2xl) 0;\n  background: linear-gradient(135deg, var(--color-red), #b91c1c);\n  text-align: center;\n}\n\n.emergencyContent h2 {\n  color: var(--color-white);\n  font-size: 2.5rem;\n  margin-bottom: var(--spacing-md);\n}\n\n.emergencyContent p {\n  color: var(--color-white);\n  font-size: 1.25rem;\n  margin-bottom: var(--spacing-xl);\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .heroContent h1 {\n    font-size: 3rem;\n  }\n\n  .heroDescription {\n    font-size: 1.125rem;\n  }\n\n  .cardsGrid {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-lg);\n  }\n\n  .infoContent {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-xl);\n  }\n\n  .stepsList li {\n    padding-left: var(--spacing-lg);\n  }\n\n  .stepsList li::before {\n    width: 1.5rem;\n    height: 1.5rem;\n    font-size: 0.75rem;\n  }\n\n  .emergencyContent h2 {\n    font-size: 2rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .heroContent h1 {\n    font-size: 2.5rem;\n  }\n\n  .cardsGrid {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-md);\n  }\n\n  .contactInfo {\n    padding: var(--spacing-lg);\n  }\n\n  .stepsList li {\n    font-size: 1rem;\n    padding-left: var(--spacing-md);\n  }\n\n  .stepsList li::before {\n    position: relative;\n    margin-right: var(--spacing-sm);\n    margin-bottom: var(--spacing-xs);\n  }\n\n  .emergencyContent h2 {\n    font-size: 1.75rem;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;;;;;AASA;;;;;AAKA;;;;;;;;AASA;;;;;AAKA;;;;;;;AAOA;;;;;AAKA;;;;;;;AAOA;;;;;;;;;;;AAWA;;;;;;;;;;;;;;;;;AAiBA;;;;AAIA;;;;;AAKA;;;;;;;;AAQA;;;;;AAKA;;;;;;AAMA;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;AAWA;EACE;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;;EAMA;;;;;AAKF;EACE;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;EAKA;;;;;;EAMA", "debugId": null}}]}