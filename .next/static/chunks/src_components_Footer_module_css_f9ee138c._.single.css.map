{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Footer.module.css"], "sourcesContent": [".footer {\n  background-color: var(--color-black);\n  border-top: 2px solid var(--color-red);\n  margin-top: var(--spacing-2xl);\n  padding: var(--spacing-2xl) 0 var(--spacing-lg);\n}\n\n.footerContent {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: var(--spacing-xl);\n  margin-bottom: var(--spacing-xl);\n}\n\n.footerSection h3 {\n  font-family: var(--font-script);\n  color: var(--color-white);\n  font-size: 2rem;\n  margin-bottom: var(--spacing-sm);\n}\n\n.footerSection h4 {\n  font-family: var(--font-script);\n  color: var(--color-red);\n  font-size: 1.5rem;\n  margin-bottom: var(--spacing-sm);\n}\n\n.tagline {\n  color: var(--color-red);\n  font-style: italic;\n  font-weight: 600;\n  margin-bottom: var(--spacing-sm);\n}\n\n.footerSection p {\n  color: var(--color-grey);\n  line-height: 1.6;\n  margin-bottom: var(--spacing-sm);\n}\n\n.footerNav {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-xs);\n}\n\n.footerLink {\n  color: var(--color-grey);\n  text-decoration: none;\n  padding: var(--spacing-xs) 0;\n  transition: color 0.3s ease;\n  border-bottom: 1px solid transparent;\n}\n\n.footerLink:hover {\n  color: var(--color-white);\n  border-bottom-color: var(--color-red);\n}\n\n.contactInfo {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-xs);\n}\n\n.contactInfo p {\n  margin-bottom: 0;\n}\n\n.contactLink {\n  color: var(--color-white);\n  text-decoration: none;\n  transition: color 0.3s ease;\n}\n\n.contactLink:hover {\n  color: var(--color-red);\n}\n\n.servicesList {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.servicesList li {\n  color: var(--color-grey);\n  padding: var(--spacing-xs) 0;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  transition: color 0.3s ease;\n}\n\n.servicesList li:hover {\n  color: var(--color-white);\n}\n\n.servicesList li:last-child {\n  border-bottom: none;\n}\n\n.footerBottom {\n  border-top: 1px solid var(--color-grey);\n  padding-top: var(--spacing-lg);\n}\n\n.footerBottomContent {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: var(--spacing-md);\n}\n\n.footerBottomContent p {\n  color: var(--color-grey);\n  margin: 0;\n}\n\n.footerCta {\n  display: flex;\n  gap: var(--spacing-sm);\n  align-items: center;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .footerContent {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-lg);\n  }\n\n  .footerBottomContent {\n    flex-direction: column;\n    text-align: center;\n    gap: var(--spacing-md);\n  }\n\n  .footerCta {\n    justify-content: center;\n  }\n}\n\n@media (max-width: 480px) {\n  .footer {\n    padding: var(--spacing-xl) 0 var(--spacing-md);\n  }\n\n  .footerContent {\n    gap: var(--spacing-md);\n  }\n\n  .footerSection h3 {\n    font-size: 1.75rem;\n  }\n\n  .footerSection h4 {\n    font-size: 1.25rem;\n  }\n\n  .footerCta {\n    flex-direction: column;\n    width: 100%;\n  }\n\n  .footerCta .btn {\n    width: 100%;\n    text-align: center;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;;;AAQA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;;;AAMA;;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;;AAOA;;;;;AAKA;;;;;;AAOA;EACE;;;;;EAKA;;;;;;EAMA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA"}}]}