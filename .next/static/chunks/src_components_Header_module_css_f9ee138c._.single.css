/* [project]/src/components/Header.module.css [app-client] (css) */
.Header-module__hBw1pG__header {
  background-color: var(--color-black);
  border-bottom: 2px solid var(--color-red);
  z-index: 1000;
  padding: var(--spacing-sm) 0;
  position: sticky;
  top: 0;
}

.Header-module__hBw1pG__headerContent {
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-md);
  display: flex;
}

.Header-module__hBw1pG__logo {
  color: var(--color-white);
  text-decoration: none;
}

.Header-module__hBw1pG__logo h1 {
  font-family: var(--font-script);
  color: var(--color-white);
  text-shadow: 2px 2px 4px #00000080;
  margin: 0;
  font-size: 2.5rem;
}

.Header-module__hBw1pG__tagline {
  font-family: var(--font-sans);
  color: var(--color-grey);
  text-transform: uppercase;
  margin-top: -.5rem;
  margin-left: 3rem;
  font-size: .65rem;
  font-weight: 800;
  display: block;
}

.Header-module__hBw1pG__nav {
  gap: var(--spacing-md);
  align-items: center;
  display: flex;
}

.Header-module__hBw1pG__navLink {
  color: var(--color-white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-weight: 500;
  text-decoration: none;
  transition: all .3s;
  position: relative;
}

.Header-module__hBw1pG__navLink:hover {
  color: var(--color-red);
  background-color: #ffffff1a;
}

.Header-module__hBw1pG__navLink:after {
  content: "";
  background-color: var(--color-red);
  width: 0;
  height: 2px;
  transition: all .3s;
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
}

.Header-module__hBw1pG__navLink:hover:after {
  width: 100%;
}

.Header-module__hBw1pG__ctaButtons {
  gap: var(--spacing-sm);
  align-items: center;
  display: flex;
}

.Header-module__hBw1pG__moreDropdown {
  display: inline-block;
  position: relative;
}

.Header-module__hBw1pG__moreButton {
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--color-white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-weight: 500;
  font-size: inherit;
  background: none;
  border: none;
  font-family: inherit;
  transition: all .3s;
  display: flex;
  position: relative;
}

.Header-module__hBw1pG__moreButton:hover {
  color: var(--color-red);
  background-color: #ffffff1a;
}

.Header-module__hBw1pG__moreButton:after {
  content: "";
  background-color: var(--color-red);
  width: 0;
  height: 2px;
  transition: all .3s;
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
}

.Header-module__hBw1pG__moreButton:hover:after {
  width: 100%;
}

.Header-module__hBw1pG__moreIcon {
  flex-shrink: 0;
  transition: transform .3s;
}

.Header-module__hBw1pG__moreIconOpen {
  transform: rotate(180deg);
}

.Header-module__hBw1pG__moreMenu {
  background-color: var(--color-black);
  border: 1px solid var(--color-grey);
  border-radius: var(--radius-sm);
  z-index: 1001;
  min-width: 160px;
  padding: var(--spacing-xs) 0;
  margin-top: var(--spacing-xs);
  animation: .2s ease-out Header-module__hBw1pG__fadeInDown;
  position: absolute;
  top: 100%;
  right: 0;
  box-shadow: 0 4px 12px #0000004d;
}

@keyframes Header-module__hBw1pG__fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.Header-module__hBw1pG__moreMenuItem {
  color: var(--color-white);
  padding: var(--spacing-sm) var(--spacing-md);
  white-space: nowrap;
  font-weight: 500;
  text-decoration: none;
  transition: all .3s;
  display: block;
}

.Header-module__hBw1pG__moreMenuItem:hover {
  color: var(--color-red);
  background-color: #ffffff1a;
}

.Header-module__hBw1pG__mobileMenuButton {
  cursor: pointer;
  padding: var(--spacing-xs);
  background: none;
  border: none;
  flex-direction: column;
  gap: 4px;
  display: none;
}

.Header-module__hBw1pG__hamburger {
  background-color: var(--color-white);
  border-radius: 2px;
  width: 25px;
  height: 3px;
  transition: all .3s;
}

.Header-module__hBw1pG__mobileNav {
  gap: var(--spacing-sm);
  padding: var(--spacing-md) 0;
  border-top: 1px solid var(--color-grey);
  margin-top: var(--spacing-sm);
  flex-direction: column;
  display: none;
}

@media (width >= 769px) {
  .Header-module__hBw1pG__mobileNav, .Header-module__hBw1pG__mobileNavOpen, .Header-module__hBw1pG__mobileMenuButton {
    display: none !important;
  }
}

.Header-module__hBw1pG__mobileNavOpen {
  display: flex;
}

.Header-module__hBw1pG__mobileNavLink {
  color: var(--color-white);
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  text-align: center;
  font-weight: 500;
  text-decoration: none;
  transition: all .3s;
}

.Header-module__hBw1pG__mobileNavLink:hover {
  color: var(--color-red);
  background-color: #ffffff1a;
}

.Header-module__hBw1pG__mobileCta {
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--color-grey);
  flex-direction: column;
  display: flex;
}

@media (width <= 768px) {
  .Header-module__hBw1pG__nav, .Header-module__hBw1pG__ctaButtons {
    display: none;
  }

  .Header-module__hBw1pG__mobileMenuButton {
    display: flex;
  }

  .Header-module__hBw1pG__logo h1 {
    font-size: 2rem;
  }
}

@media (width <= 480px) {
  .Header-module__hBw1pG__logo h1 {
    font-size: 1.75rem;
  }

  .Header-module__hBw1pG__headerContent {
    gap: var(--spacing-sm);
  }
}

/*# sourceMappingURL=src_components_Header_module_css_f9ee138c._.single.css.map*/