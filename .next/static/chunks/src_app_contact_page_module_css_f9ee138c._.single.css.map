{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/contact/page.module.css"], "sourcesContent": [".contactPage {\n  min-height: 100vh;\n}\n\n/* Hero Section */\n.hero {\n  padding: var(--spacing-2xl) 0;\n  background: linear-gradient(135deg, var(--color-black) 0%, #1a1a1a 100%);\n  text-align: center;\n}\n\n.heroContent h1 {\n  font-size: 4rem;\n  color: var(--color-white);\n  margin-bottom: var(--spacing-md);\n}\n\n.heroDescription {\n  font-size: 1.25rem;\n  color: var(--color-grey);\n  max-width: 700px;\n  margin: 0 auto;\n  line-height: 1.6;\n}\n\n/* Contact Information */\n.contactInfo {\n  padding: var(--spacing-2xl) 0;\n  background-color: #111111;\n}\n\n.contactGrid {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: var(--spacing-2xl);\n  align-items: start;\n}\n\n.contactDetails h2 {\n  color: var(--color-white);\n  margin-bottom: var(--spacing-xl);\n}\n\n.contactItem {\n  display: flex;\n  gap: var(--spacing-md);\n  margin-bottom: var(--spacing-xl);\n  padding: var(--spacing-lg);\n  background-color: var(--color-black);\n  border: 2px solid var(--color-grey);\n  border-radius: var(--radius-lg);\n  transition: all 0.3s ease;\n}\n\n.contactItem:hover {\n  border-color: var(--color-red);\n  transform: translateY(-2px);\n}\n\n.contactIcon {\n  font-size: 2rem;\n  flex-shrink: 0;\n  line-height: 1;\n}\n\n.contactText h3 {\n  color: var(--color-white);\n  margin: 0 0 var(--spacing-xs) 0;\n  font-size: 1.25rem;\n}\n\n.contactText p {\n  margin: 0 0 var(--spacing-xs) 0;\n}\n\n.contactLink {\n  color: var(--color-red);\n  text-decoration: none;\n  font-weight: 600;\n  font-size: 1.125rem;\n  transition: color 0.3s ease;\n}\n\n.contactLink:hover {\n  color: var(--color-white);\n}\n\n.contactNote {\n  color: var(--color-grey);\n  font-size: 0.875rem;\n  font-style: italic;\n}\n\n.businessInfo {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-lg);\n}\n\n.hoursCard,\n.servicesCard {\n  background-color: var(--color-black);\n  border: 2px solid var(--color-red);\n  border-radius: var(--radius-lg);\n  padding: var(--spacing-xl);\n}\n\n.hoursCard h3,\n.servicesCard h3 {\n  color: var(--color-white);\n  margin-bottom: var(--spacing-md);\n  text-align: center;\n}\n\n.hours {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-sm);\n  margin-bottom: var(--spacing-md);\n}\n\n.hourItem {\n  display: flex;\n  justify-content: space-between;\n  color: var(--color-grey);\n  padding: var(--spacing-xs) 0;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.hourItem:last-child {\n  border-bottom: none;\n}\n\n.emergencyNote {\n  color: var(--color-red);\n  font-weight: 600;\n  text-align: center;\n  margin: 0;\n  padding: var(--spacing-sm);\n  background-color: rgba(220, 38, 38, 0.1);\n  border-radius: var(--radius-sm);\n}\n\n.servicesList {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.servicesList li {\n  color: var(--color-grey);\n  padding: var(--spacing-xs) 0;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  transition: color 0.3s ease;\n}\n\n.servicesList li:hover {\n  color: var(--color-white);\n}\n\n.servicesList li:last-child {\n  border-bottom: none;\n}\n\n.servicesList li::before {\n  content: '✓ ';\n  color: var(--color-red);\n  font-weight: bold;\n}\n\n/* Map Section */\n.mapSection {\n  padding: var(--spacing-2xl) 0;\n  background-color: var(--color-black);\n}\n\n.mapContainer {\n  margin-bottom: var(--spacing-xl);\n}\n\n.mapPlaceholder {\n  background: linear-gradient(135deg, #111111, #2a2a2a);\n  border: 2px solid var(--color-red);\n  border-radius: var(--radius-lg);\n  padding: var(--spacing-2xl);\n  text-align: center;\n  min-height: 300px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n}\n\n.mapPlaceholder h3 {\n  color: var(--color-white);\n  font-size: 2rem;\n  margin-bottom: var(--spacing-md);\n}\n\n.mapPlaceholder p {\n  color: var(--color-grey);\n  font-size: 1.125rem;\n  margin-bottom: var(--spacing-sm);\n}\n\n.serviceAreas {\n  text-align: center;\n}\n\n.serviceAreas h4 {\n  color: var(--color-white);\n  margin-bottom: var(--spacing-md);\n}\n\n.areasList {\n  display: flex;\n  flex-wrap: wrap;\n  gap: var(--spacing-sm);\n  justify-content: center;\n}\n\n.areasList span {\n  background-color: #111111;\n  color: var(--color-grey);\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--radius-sm);\n  border: 1px solid var(--color-grey);\n  transition: all 0.3s ease;\n}\n\n.areasList span:hover {\n  background-color: var(--color-red);\n  color: var(--color-white);\n  border-color: var(--color-red);\n}\n\n/* Emergency Section */\n.emergency {\n  padding: var(--spacing-xl) 0;\n  background: linear-gradient(135deg, var(--color-red), #b91c1c);\n  text-align: center;\n}\n\n.emergencyContent h2 {\n  color: var(--color-white);\n  font-size: 2.5rem;\n  margin-bottom: var(--spacing-md);\n}\n\n.emergencyContent p {\n  color: var(--color-white);\n  font-size: 1.25rem;\n  margin-bottom: var(--spacing-xl);\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n}\n\n/* CTA Section */\n.cta {\n  padding: var(--spacing-2xl) 0;\n  background-color: #111111;\n  text-align: center;\n}\n\n.ctaContent h2 {\n  color: var(--color-white);\n  font-size: 2.5rem;\n  margin-bottom: var(--spacing-md);\n}\n\n.ctaContent p {\n  color: var(--color-grey);\n  font-size: 1.25rem;\n  margin-bottom: var(--spacing-xl);\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .heroContent h1 {\n    font-size: 3rem;\n  }\n\n  .heroDescription {\n    font-size: 1.125rem;\n  }\n\n  .contactGrid {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-xl);\n  }\n\n  .contactItem {\n    padding: var(--spacing-md);\n  }\n\n  .hoursCard,\n  .servicesCard {\n    padding: var(--spacing-lg);\n  }\n\n  .mapPlaceholder {\n    padding: var(--spacing-xl);\n    min-height: 200px;\n  }\n\n  .emergencyContent h2 {\n    font-size: 2rem;\n  }\n\n  .ctaContent h2 {\n    font-size: 2rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .heroContent h1 {\n    font-size: 2.5rem;\n  }\n\n  .contactItem {\n    flex-direction: column;\n    text-align: center;\n    gap: var(--spacing-sm);\n  }\n\n  .contactIcon {\n    align-self: center;\n  }\n\n  .mapPlaceholder {\n    padding: var(--spacing-lg);\n  }\n\n  .mapPlaceholder h3 {\n    font-size: 1.5rem;\n  }\n\n  .areasList {\n    gap: var(--spacing-xs);\n  }\n\n  .areasList span {\n    font-size: 0.875rem;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;;;;;AASA;;;;;AAKA;;;;;;;AAOA;;;;;AAKA;;;;;;;;;;;AAWA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;;;;;AAQA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;;;;AAQA;;;;;;AAOA;;;;;;;AAOA;;;;;;;;AAQA;;;;AAIA;;;;;;;;;;AAUA;;;;;;AAMA;;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;;;AAOA;;;;;AAKA;;;;AAIA;;;;;;;;;;;;;AAaA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;;AAKA;;;;;;;AAOA;;;;;;;;;AASA;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;AAWA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;AAWA;EACE;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAKA;;;;;EAKA;;;;;AASF;EACE;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA"}}]}