/* [project]/src/app/bookings/page.module.css [app-client] (css) */
.page-module__rNCILW__bookingsPage {
  min-height: 100vh;
}

.page-module__rNCILW__hero {
  padding: var(--spacing-2xl) 0;
  background: linear-gradient(135deg, var(--color-black) 0%, #1a1a1a 100%);
  text-align: center;
}

.page-module__rNCILW__heroContent h1 {
  color: var(--color-white);
  margin-bottom: var(--spacing-md);
  font-size: 4rem;
}

.page-module__rNCILW__heroDescription {
  color: var(--color-grey);
  max-width: 700px;
  margin: 0 auto;
  font-size: 1.25rem;
  line-height: 1.6;
}

.page-module__rNCILW__bookingCards {
  padding: var(--spacing-2xl) 0;
  background-color: #111;
}

.page-module__rNCILW__cardsGrid {
  gap: var(--spacing-xl);
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
}

.page-module__rNCILW__bookingInfo {
  padding: var(--spacing-2xl) 0;
  background-color: var(--color-black);
}

.page-module__rNCILW__infoContent {
  gap: var(--spacing-2xl);
  grid-template-columns: 2fr 1fr;
  align-items: start;
  display: grid;
}

.page-module__rNCILW__infoText h2 {
  color: var(--color-white);
  margin-bottom: var(--spacing-lg);
}

.page-module__rNCILW__stepsList {
  counter-reset: step-counter;
  margin: 0;
  padding: 0;
  list-style: none;
}

.page-module__rNCILW__stepsList li {
  color: var(--color-grey);
  padding: var(--spacing-md) 0;
  counter-increment: step-counter;
  padding-left: var(--spacing-xl);
  border-bottom: 1px solid #ffffff1a;
  font-size: 1.125rem;
  line-height: 1.6;
  position: relative;
}

.page-module__rNCILW__stepsList li:before {
  content: counter(step-counter);
  left: 0;
  top: var(--spacing-md);
  background-color: var(--color-red);
  width: 2rem;
  height: 2rem;
  color: var(--color-white);
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  font-size: .875rem;
  font-weight: bold;
  display: flex;
  position: absolute;
}

.page-module__rNCILW__stepsList li:last-child {
  border-bottom: none;
}

.page-module__rNCILW__stepsList strong {
  color: var(--color-red);
  font-weight: 600;
}

.page-module__rNCILW__contactInfo {
  border: 2px solid var(--color-grey);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  text-align: center;
  background-color: #111;
}

.page-module__rNCILW__contactInfo h3 {
  color: var(--color-white);
  margin-bottom: var(--spacing-md);
}

.page-module__rNCILW__contactInfo p {
  color: var(--color-grey);
  margin-bottom: var(--spacing-lg);
  line-height: 1.6;
}

.page-module__rNCILW__contactOptions {
  gap: var(--spacing-sm);
  flex-direction: column;
  display: flex;
}

.page-module__rNCILW__emergency {
  padding: var(--spacing-2xl) 0;
  background: linear-gradient(135deg, var(--color-red), #b91c1c);
  text-align: center;
}

.page-module__rNCILW__emergencyContent h2 {
  color: var(--color-white);
  margin-bottom: var(--spacing-md);
  font-size: 2.5rem;
}

.page-module__rNCILW__emergencyContent p {
  color: var(--color-white);
  margin-bottom: var(--spacing-xl);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  font-size: 1.25rem;
  line-height: 1.6;
}

@media (width <= 768px) {
  .page-module__rNCILW__heroContent h1 {
    font-size: 3rem;
  }

  .page-module__rNCILW__heroDescription {
    font-size: 1.125rem;
  }

  .page-module__rNCILW__cardsGrid {
    gap: var(--spacing-lg);
    grid-template-columns: 1fr;
  }

  .page-module__rNCILW__infoContent {
    gap: var(--spacing-xl);
    grid-template-columns: 1fr;
  }

  .page-module__rNCILW__stepsList li {
    padding-left: var(--spacing-lg);
  }

  .page-module__rNCILW__stepsList li:before {
    width: 1.5rem;
    height: 1.5rem;
    font-size: .75rem;
  }

  .page-module__rNCILW__emergencyContent h2 {
    font-size: 2rem;
  }
}

@media (width <= 480px) {
  .page-module__rNCILW__heroContent h1 {
    font-size: 2.5rem;
  }

  .page-module__rNCILW__cardsGrid {
    gap: var(--spacing-md);
    grid-template-columns: 1fr;
  }

  .page-module__rNCILW__contactInfo {
    padding: var(--spacing-lg);
  }

  .page-module__rNCILW__stepsList li {
    padding-left: var(--spacing-md);
    font-size: 1rem;
  }

  .page-module__rNCILW__stepsList li:before {
    margin-right: var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
    position: relative;
  }

  .page-module__rNCILW__emergencyContent h2 {
    font-size: 1.75rem;
  }
}

/*# sourceMappingURL=src_app_bookings_page_module_css_f9ee138c._.single.css.map*/