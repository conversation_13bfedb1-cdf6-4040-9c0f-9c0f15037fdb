{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/BookingCard.module.css"], "sourcesContent": [".bookingCard {\n  background-color: var(--color-black);\n  border: 2px solid var(--color-grey);\n  border-radius: var(--radius-lg);\n  padding: var(--spacing-xl);\n  transition: all 0.3s ease;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  overflow: hidden;\n}\n\n.bookingCard:hover {\n  border-color: var(--color-red);\n  transform: translateY(-5px);\n  box-shadow: var(--shadow-lg);\n}\n\n.urgent {\n  border-color: var(--color-red);\n  background: linear-gradient(135deg, var(--color-black), #1a0000);\n}\n\n.urgent:hover {\n  border-color: var(--color-white);\n  box-shadow: 0 10px 25px rgba(220, 38, 38, 0.3);\n}\n\n.cardHeader {\n  display: flex;\n  align-items: flex-start;\n  gap: var(--spacing-md);\n  margin-bottom: var(--spacing-md);\n}\n\n.cardIcon {\n  font-size: 3rem;\n  flex-shrink: 0;\n  line-height: 1;\n}\n\n.cardTitle h3 {\n  color: var(--color-white);\n  margin: 0 0 var(--spacing-xs) 0;\n  font-size: 1.5rem;\n  line-height: 1.2;\n}\n\n.duration {\n  color: var(--color-red);\n  font-size: 0.875rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.1em;\n}\n\n.cardContent {\n  flex-grow: 1;\n  margin-bottom: var(--spacing-lg);\n}\n\n.description {\n  color: var(--color-grey);\n  line-height: 1.6;\n  margin: 0;\n  font-size: 1rem;\n}\n\n.cardFooter {\n  margin-top: auto;\n}\n\n.bookingButton {\n  width: 100%;\n  text-align: center;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n}\n\n.urgentBadge {\n  position: absolute;\n  top: var(--spacing-sm);\n  right: var(--spacing-sm);\n  background-color: var(--color-red);\n  color: var(--color-white);\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--radius-sm);\n  font-size: 0.75rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.1em;\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% {\n    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0.7);\n  }\n  70% {\n    box-shadow: 0 0 0 10px rgba(220, 38, 38, 0);\n  }\n  100% {\n    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0);\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .bookingCard {\n    padding: var(--spacing-lg);\n  }\n\n  .cardHeader {\n    flex-direction: column;\n    text-align: center;\n    gap: var(--spacing-sm);\n  }\n\n  .cardIcon {\n    font-size: 2.5rem;\n    align-self: center;\n  }\n\n  .cardTitle h3 {\n    font-size: 1.25rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .bookingCard {\n    padding: var(--spacing-md);\n  }\n\n  .cardIcon {\n    font-size: 2rem;\n  }\n\n  .urgentBadge {\n    position: static;\n    margin-bottom: var(--spacing-sm);\n    text-align: center;\n    display: inline-block;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;AAaA;;;;;;AAMA;;;;;AAKA;;;;;AAKA;;;;;;;AAOA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;AAQA;;;;;AAKA;;;;;;;AAOA;;;;AAIA;;;;;;;;AAQA;;;;;;;;;;;;;;;AAeA;;;;;;;;;;;;;;AAaA;EACE;;;;EAIA;;;;;;EAMA;;;;;EAKA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA"}}]}