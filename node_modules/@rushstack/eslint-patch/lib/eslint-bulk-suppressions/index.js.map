{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/eslint-bulk-suppressions/index.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;AAE3D,gDAA8C;AAC9C,6CAA4G;AAC5G,uEAA6E;AAC7E,mEAAoF;AACpF,2CAAmG;AAEnG,IAAI,CAAC,0BAAY,EAAE,CAAC;IAClB,OAAO,CAAC,KAAK,CACX,gGAAgG,CACjG,CAAC;IAEF,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC;AAED,MAAM,2BAA2B,GAAuB,OAAO,CAAC,GAAG,CAAC,2CAA+B,CAAC,CAAC;AACrG,IAAI,2BAA2B,KAAK,MAAM,IAAI,2BAA2B,KAAK,GAAG,EAAE,CAAC;IAClF,IAAA,0CAA6B,GAAE,CAAC;IAChC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC;AAED,MAAM,cAAc,GAAW,IAAA,8BAAiB,GAAE,CAAC;AAEnD,OAAO,CAAC,GAAG,CAAC,+CAAmC,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;AAEhG,MAAM,oBAAoB,GAAW,IAAA,uCAA0B,GAAE,CAAC;AAClE,IAAA,iEAAyC,EAAC,cAAc,EAAE,oBAAoB,CAAC,CAAC;AAChF,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAC9D,WAAW,CAAC,SAAS,CAAC,MAAM,GAAG,IAAA,8CAAoB,EAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AAElF,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAE3C,IAAA,oCAAU,EAAC,MAAM,EAAE,WAAW,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport { eslintFolder } from '../_patch-base';\nimport { findAndConsoleLogPatchPathCli, getPathToLinterJS, ensurePathToGeneratedPatch } from './path-utils';\nimport { patchClass, extendVerifyFunction } from './bulk-suppressions-patch';\nimport { generatePatchedLinterJsFileIfDoesNotExist } from './generate-patched-file';\nimport { ESLINT_BULK_DETECT_ENV_VAR_NAME, ESLINT_BULK_PATCH_PATH_ENV_VAR_NAME } from './constants';\n\nif (!eslintFolder) {\n  console.error(\n    '@rushstack/eslint-patch/eslint-bulk-suppressions: Could not find ESLint installation to patch.'\n  );\n\n  process.exit(1);\n}\n\nconst eslintBulkDetectEnvVarValue: string | undefined = process.env[ESLINT_BULK_DETECT_ENV_VAR_NAME];\nif (eslintBulkDetectEnvVarValue === 'true' || eslintBulkDetectEnvVarValue === '1') {\n  findAndConsoleLogPatchPathCli();\n  process.exit(0);\n}\n\nconst pathToLinterJS: string = getPathToLinterJS();\n\nprocess.env[ESLINT_BULK_PATCH_PATH_ENV_VAR_NAME] = require.resolve('./bulk-suppressions-patch');\n\nconst pathToGeneratedPatch: string = ensurePathToGeneratedPatch();\ngeneratePatchedLinterJsFileIfDoesNotExist(pathToLinterJS, pathToGeneratedPatch);\nconst { Linter: LinterPatch } = require(pathToGeneratedPatch);\nLinterPatch.prototype.verify = extendVerifyFunction(LinterPatch.prototype.verify);\n\nconst { Linter } = require(pathToLinterJS);\n\npatchClass(Linter, LinterPatch);\n"]}