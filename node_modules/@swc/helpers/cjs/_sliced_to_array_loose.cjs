"use strict";

var _array_with_holes = require("./_array_with_holes.cjs");
var _iterable_to_array_limit_loose = require("./_iterable_to_array_limit_loose.cjs");
var _non_iterable_rest = require("./_non_iterable_rest.cjs");
var _unsupported_iterable_to_array = require("./_unsupported_iterable_to_array.cjs");

function _sliced_to_array_loose(arr, i) {
    return _array_with_holes._(arr) || _iterable_to_array_limit_loose._(arr, i) || _unsupported_iterable_to_array._(arr, i) || _non_iterable_rest._();
}
exports._ = _sliced_to_array_loose;
