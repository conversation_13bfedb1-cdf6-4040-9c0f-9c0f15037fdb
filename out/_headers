# Security and Performance Headers for Tuffside Website

/*
  # Security Headers
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()
  
  # Content Security Policy
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://www.google-analytics.com; frame-src 'self'
  
  # Performance Headers
  Cache-Control: public, max-age=31536000, immutable

# Static Assets Caching
/images/*
  Cache-Control: public, max-age=31536000, immutable

/_next/static/*
  Cache-Control: public, max-age=31536000, immutable

# Fonts Caching
*.woff2
  Cache-Control: public, max-age=31536000, immutable
  
*.woff
  Cache-Control: public, max-age=31536000, immutable

# HTML Files
*.html
  Cache-Control: public, max-age=3600

# API Routes (if any)
/api/*
  Cache-Control: no-cache, no-store, must-revalidate
