.BookingCard_bookingCard__WuUmf{background-color:var(--color-black);border:2px solid var(--color-grey);border-radius:var(--radius-lg);padding:var(--spacing-xl);transition:all .3s ease;height:100%;display:flex;flex-direction:column;position:relative;overflow:hidden}.BookingCard_bookingCard__WuUmf:hover{border-color:var(--color-red);transform:translateY(-5px);box-shadow:var(--shadow-lg)}.BookingCard_urgent__pCzbB{border-color:var(--color-red);background:linear-gradient(135deg,var(--color-black),#1a0000)}.BookingCard_urgent__pCzbB:hover{border-color:var(--color-white);box-shadow:0 10px 25px rgba(220,38,38,.3)}.BookingCard_cardHeader__ciU57{display:flex;align-items:flex-start;gap:var(--spacing-md);margin-bottom:var(--spacing-md)}.BookingCard_cardIcon__6nQbt{font-size:3rem;flex-shrink:0;line-height:1}.BookingCard_cardTitle__oTzgC h3{color:var(--color-white);margin:0 0 var(--spacing-xs) 0;font-size:1.5rem;line-height:1.2}.BookingCard_duration__Cdcue{color:var(--color-red);font-size:.875rem;font-weight:600;text-transform:uppercase;letter-spacing:.1em}.BookingCard_cardContent__ndqG9{flex-grow:1;margin-bottom:var(--spacing-lg)}.BookingCard_description__tobEJ{color:var(--color-grey);line-height:1.6;margin:0;font-size:1rem}.BookingCard_cardFooter__bMU9z{margin-top:auto}.BookingCard_bookingButton__aurgb{width:100%;text-align:center;font-weight:600;text-transform:uppercase;letter-spacing:.05em}.BookingCard_urgentBadge__PJOlj{position:absolute;top:var(--spacing-sm);right:var(--spacing-sm);background-color:var(--color-red);color:var(--color-white);padding:var(--spacing-xs) var(--spacing-sm);border-radius:var(--radius-sm);font-size:.75rem;font-weight:600;text-transform:uppercase;letter-spacing:.1em;animation:BookingCard_pulse__0oHHt 2s infinite}@keyframes BookingCard_pulse__0oHHt{0%{box-shadow:0 0 0 0 rgba(220,38,38,.7)}70%{box-shadow:0 0 0 10px rgba(220,38,38,0)}to{box-shadow:0 0 0 0 rgba(220,38,38,0)}}@media (max-width:768px){.BookingCard_bookingCard__WuUmf{padding:var(--spacing-lg)}.BookingCard_cardHeader__ciU57{flex-direction:column;text-align:center;gap:var(--spacing-sm)}.BookingCard_cardIcon__6nQbt{font-size:2.5rem;align-self:center}.BookingCard_cardTitle__oTzgC h3{font-size:1.25rem}}@media (max-width:480px){.BookingCard_bookingCard__WuUmf{padding:var(--spacing-md)}.BookingCard_cardIcon__6nQbt{font-size:2rem}.BookingCard_urgentBadge__PJOlj{position:static;margin-bottom:var(--spacing-sm);text-align:center;display:inline-block}}.page_bookingsPage__ljLgN{min-height:100vh}.page_hero__c0n1a{padding:var(--spacing-2xl) 0;background:linear-gradient(135deg,var(--color-black) 0,#1a1a1a 100%);text-align:center}.page_heroContent__seQKr h1{font-size:4rem;color:var(--color-white);margin-bottom:var(--spacing-md)}.page_heroDescription__iDNso{font-size:1.25rem;color:var(--color-grey);max-width:700px;margin:0 auto;line-height:1.6}.page_bookingCards__KkYNQ{padding:var(--spacing-2xl) 0;background-color:#111111}.page_cardsGrid__6AX3m{display:grid;grid-template-columns:repeat(auto-fit,minmax(350px,1fr));grid-gap:var(--spacing-xl);gap:var(--spacing-xl);max-width:1200px;margin:0 auto}.page_bookingInfo__J7Pur{padding:var(--spacing-2xl) 0;background-color:var(--color-black)}.page_infoContent__aEVv3{display:grid;grid-template-columns:2fr 1fr;grid-gap:var(--spacing-2xl);gap:var(--spacing-2xl);align-items:start}.page_infoText__ourpa h2{color:var(--color-white);margin-bottom:var(--spacing-lg)}.page_stepsList__MDlBI{list-style:none;padding:0;margin:0;counter-reset:step-counter}.page_stepsList__MDlBI li{color:var(--color-grey);padding:var(--spacing-md) 0;border-bottom:1px solid rgba(255,255,255,.1);font-size:1.125rem;line-height:1.6;counter-increment:step-counter;position:relative;padding-left:var(--spacing-xl)}.page_stepsList__MDlBI li:before{content:counter(step-counter);position:absolute;left:0;top:var(--spacing-md);width:2rem;height:2rem;background-color:var(--color-red);color:var(--color-white);border-radius:50%;display:flex;align-items:center;justify-content:center;font-weight:700;font-size:.875rem}.page_stepsList__MDlBI li:last-child{border-bottom:none}.page_stepsList__MDlBI strong{color:var(--color-red);font-weight:600}.page_contactInfo__VQAOc{background-color:#111111;border:2px solid var(--color-grey);border-radius:var(--radius-lg);padding:var(--spacing-xl);text-align:center}.page_contactInfo__VQAOc h3{color:var(--color-white);margin-bottom:var(--spacing-md)}.page_contactInfo__VQAOc p{color:var(--color-grey);line-height:1.6;margin-bottom:var(--spacing-lg)}.page_contactOptions__XS_NU{display:flex;flex-direction:column;gap:var(--spacing-sm)}.page_emergency__B_Jfx{padding:var(--spacing-2xl) 0;background:linear-gradient(135deg,var(--color-red),#b91c1c);text-align:center}.page_emergencyContent__c4s67 h2{color:var(--color-white);font-size:2.5rem;margin-bottom:var(--spacing-md)}.page_emergencyContent__c4s67 p{color:var(--color-white);font-size:1.25rem;margin-bottom:var(--spacing-xl);max-width:600px;margin-left:auto;margin-right:auto;line-height:1.6}@media (max-width:768px){.page_heroContent__seQKr h1{font-size:3rem}.page_heroDescription__iDNso{font-size:1.125rem}.page_cardsGrid__6AX3m{grid-template-columns:1fr;gap:var(--spacing-lg)}.page_infoContent__aEVv3{grid-template-columns:1fr;gap:var(--spacing-xl)}.page_stepsList__MDlBI li{padding-left:var(--spacing-lg)}.page_stepsList__MDlBI li:before{width:1.5rem;height:1.5rem;font-size:.75rem}.page_emergencyContent__c4s67 h2{font-size:2rem}}@media (max-width:480px){.page_heroContent__seQKr h1{font-size:2.5rem}.page_cardsGrid__6AX3m{grid-template-columns:1fr;gap:var(--spacing-md)}.page_contactInfo__VQAOc{padding:var(--spacing-lg)}.page_stepsList__MDlBI li{font-size:1rem;padding-left:var(--spacing-md)}.page_stepsList__MDlBI li:before{position:relative;margin-right:var(--spacing-sm);margin-bottom:var(--spacing-xs)}.page_emergencyContent__c4s67 h2{font-size:1.75rem}}