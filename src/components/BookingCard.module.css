.bookingCard {
  background-color: var(--color-black);
  border: 2px solid var(--color-grey);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.bookingCard:hover {
  border-color: var(--color-red);
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.urgent {
  border-color: var(--color-red);
  background: linear-gradient(135deg, var(--color-black), #1a0000);
}

.urgent:hover {
  border-color: var(--color-white);
  box-shadow: 0 10px 25px rgba(220, 38, 38, 0.3);
}

.cardHeader {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.cardIcon {
  font-size: 3rem;
  flex-shrink: 0;
  line-height: 1;
}

.cardTitle h3 {
  color: var(--color-white);
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 1.5rem;
  line-height: 1.2;
}

.duration {
  color: var(--color-red);
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.cardContent {
  flex-grow: 1;
  margin-bottom: var(--spacing-lg);
}

.description {
  color: var(--color-grey);
  line-height: 1.6;
  margin: 0;
  font-size: 1rem;
}

.cardFooter {
  margin-top: auto;
}

.bookingButton {
  width: 100%;
  text-align: center;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.urgentBadge {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  background-color: var(--color-red);
  color: var(--color-white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(220, 38, 38, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .bookingCard {
    padding: var(--spacing-lg);
  }

  .cardHeader {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-sm);
  }

  .cardIcon {
    font-size: 2.5rem;
    align-self: center;
  }

  .cardTitle h3 {
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .bookingCard {
    padding: var(--spacing-md);
  }

  .cardIcon {
    font-size: 2rem;
  }

  .urgentBadge {
    position: static;
    margin-bottom: var(--spacing-sm);
    text-align: center;
    display: inline-block;
  }
}
