.footer {
  background-color: var(--color-black);
  border-top: 2px solid var(--color-red);
  margin-top: var(--spacing-2xl);
  padding: var(--spacing-2xl) 0 var(--spacing-lg);
}

.footerContent {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

.footerSection h3 {
  font-family: var(--font-script);
  color: var(--color-white);
  font-size: 2rem;
  margin-bottom: var(--spacing-sm);
}

.footerSection h4 {
  font-family: var(--font-script);
  color: var(--color-red);
  font-size: 1.5rem;
  margin-bottom: var(--spacing-sm);
}

.tagline {
  color: var(--color-red);
  font-style: italic;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
}

.footerSection p {
  color: var(--color-grey);
  line-height: 1.6;
  margin-bottom: var(--spacing-sm);
}

.footerNav {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.footerLink {
  color: var(--color-grey);
  text-decoration: none;
  padding: var(--spacing-xs) 0;
  transition: color 0.3s ease;
  border-bottom: 1px solid transparent;
}

.footerLink:hover {
  color: var(--color-white);
  border-bottom-color: var(--color-red);
}

.contactInfo {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.contactInfo p {
  margin-bottom: 0;
}

.contactLink {
  color: var(--color-white);
  text-decoration: none;
  transition: color 0.3s ease;
}

.contactLink:hover {
  color: var(--color-red);
}

.servicesList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.servicesList li {
  color: var(--color-grey);
  padding: var(--spacing-xs) 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: color 0.3s ease;
}

.servicesList li:hover {
  color: var(--color-white);
}

.servicesList li:last-child {
  border-bottom: none;
}

.footerBottom {
  border-top: 1px solid var(--color-grey);
  padding-top: var(--spacing-lg);
}

.footerBottomContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-md);
}

.footerBottomContent p {
  color: var(--color-grey);
  margin: 0;
}

.footerCta {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .footerContent {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .footerBottomContent {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
  }

  .footerCta {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .footer {
    padding: var(--spacing-xl) 0 var(--spacing-md);
  }

  .footerContent {
    gap: var(--spacing-md);
  }

  .footerSection h3 {
    font-size: 1.75rem;
  }

  .footerSection h4 {
    font-size: 1.25rem;
  }

  .footerCta {
    flex-direction: column;
    width: 100%;
  }

  .footerCta .btn {
    width: 100%;
    text-align: center;
  }
}
