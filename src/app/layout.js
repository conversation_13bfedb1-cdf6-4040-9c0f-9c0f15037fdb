import { <PERSON>, <PERSON>_<PERSON><PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import Header from "@/components/Header";
import Footer from "@/components/Footer";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

const mrDafoe = <PERSON>_<PERSON>foe({
  variable: "--font-mr-dafoe",
  subsets: ["latin"],
  weight: "400",
});

export const metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'https://tuffside.com'),
  title: "Tuffside Automotive Garage - Built to Last. Tuned to Perform.",
  description: "Professional automotive repair and maintenance services in Trinidad. Expert diagnostics, diesel repair, engine tuning, and suspension work. Call +1 868 335-7440 or book online.",
  keywords: "automotive repair, car service, diesel repair, engine tuning, suspension, Trinidad garage, vehicle maintenance",
  icons: {
    icon: '/images/favicon.png',
    shortcut: '/images/favicon.png',
    apple: '/images/favicon.png',
  },
  openGraph: {
    title: "Tuffside Automotive Garage",
    description: "Professional automotive repair and maintenance services in Trinidad",
    type: "website",
    images: [
      {
        url: '/images/favicon.png',
        width: 32,
        height: 32,
        alt: 'Tuffside Automotive Garage Logo',
      },
    ],
  },
  twitter: {
    card: 'summary',
    title: "Tuffside Automotive Garage",
    description: "Professional automotive repair and maintenance services in Trinidad",
    images: ['/images/favicon.png'],
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#ffffff" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Tuffside" />
      </head>
      <body className={`${inter.variable} ${mrDafoe.variable}`}>
        <Header />
        <main>{children}</main>
        <Footer />
      </body>
    </html>
  );
}
